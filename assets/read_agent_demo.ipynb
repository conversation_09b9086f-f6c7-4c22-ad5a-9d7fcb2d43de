{"cells": [{"cell_type": "markdown", "metadata": {"id": "1iqyV7VcsiXT"}, "source": ["![read_agent_teaser](https://read-agent.github.io/img/teaser.png)"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "MYOnCMh83ZRE"}, "outputs": [], "source": ["import re\n", "import time\n", "import datetime\n", "import json\n", "import string\n", "import copy\n", "import requests"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "pYm2GsBGEvAI"}, "outputs": [], "source": ["API_URL = 'http://192.168.3.70:1234/v1/chat/completions'\n", "API_KEY = 'lm_studio'\n", "MODEL = 'qwen3-30b-a3b'\n", "\n", "\n", "def query_model(\n", "    prompt: str,\n", "    enable_thinking: bool = False,\n", "    temperature: float = 0.0,\n", "    max_decode_steps: int = 4096,\n", "    seconds_to_reset_tokens: float = 30.0,\n", "    max_retries: int = 5\n", ") -> str:\n", "    headers = {\n", "        'Authorization': f'<PERSON><PERSON> {API_KEY}',\n", "        'Content-Type': 'application/json'\n", "    }\n", "\n", "    payload = {\n", "        'model': MODEL,\n", "        'messages': [\n", "            {\"role\": \"system\", \"content\": \"You are a helpful assistant.\"},\n", "            {\"role\": \"user\", \"content\": prompt + \"\\n\\n /no_think\"}\n", "        ],\n", "        'temperature': temperature,\n", "        'max_tokens': max_decode_steps,\n", "        'enable_thinking': enable_thinking\n", "    }\n", "\n", "    retry_count = 0\n", "    while retry_count < max_retries:\n", "        try:\n", "            response = requests.post(\n", "                API_URL,\n", "                headers=headers,\n", "                json=payload,\n", "                timeout=300\n", "            )\n", "            response.raise_for_status()\n", "\n", "            # LM Studio返回格式解析\n", "            data = response.json()\n", "            # 调试输出，查看原始响应\n", "            # print(f\"Original response from LM Studio: {data}\")\n", "            if isinstance(data, dict):\n", "                # 检查并从OpenAI兼容的choices格式中提取内容\n", "                if 'choices' in data and data['choices']:\n", "                    response_text = data['choices'][0].get(\n", "                        'message', {}).get('content', '')\n", "                else:\n", "                    # 兼容旧版或直接返回内容的格式\n", "                    response_text = data.get('content', '')\n", "            elif isinstance(data, str):\n", "                # 旧版可能直接返回字符串\n", "                response_text = data\n", "            else:\n", "                response_text = str(data)\n", "\n", "            cleaned_text = re.sub(r\"<think>.*?</think>\",\n", "                                  \"\", response_text, flags=re.DOTALL)\n", "            return cleaned_text\n", "\n", "        except requests.exceptions.Timeout:\n", "            print(f'{datetime.datetime.now()}: query_lm_model: 请求超时')\n", "            retry_count += 1\n", "            time.sleep(5)\n", "        except requests.exceptions.HTTPError as e:\n", "            if e.response.status_code == 429:\n", "                print(f'{datetime.datetime.now()}: query_lm_model: 请求过于频繁: {e}')\n", "                time.sleep(seconds_to_reset_tokens)\n", "            else:\n", "                print(f'{datetime.datetime.now()}: query_lm_model: HTTP错误: {e}')\n", "                retry_count += 1\n", "                time.sleep(5)\n", "        except Exception as e:\n", "            print(f'{datetime.datetime.now()}: query_lm_model: 未知错误: {e}')\n", "            retry_count += 1\n", "            time.sleep(5)\n", "\n", "    raise Exception(f'达到最大重试次数({max_retries})仍未能成功请求API')"]}, {"cell_type": "code", "execution_count": null, "metadata": {"cellView": "form", "id": "1B70Rqg97aXu"}, "outputs": [], "source": ["# @title Load a QuALITY example\n", "\n", "# Fields that are straight text copies from raw example to processed example.\n", "_ONE2ONE_FIELDS = (\n", "    'article',\n", "    'article_id',\n", "    'set_unique_id',\n", "    'writer_id',\n", "    'source',\n", "    'title',\n", "    'topic',\n", "    'url',\n", "    'writer_id',\n", "    'author',\n", ")\n", "\n", "quality_dev = []\n", "\n", "with open('QuALITY.v1.0.1.htmlstripped.dev', 'r') as f:\n", "    for line in f.readlines():\n", "        j = json.loads(line)\n", "        fields = {k: j[k] for k in _ONE2ONE_FIELDS}\n", "        fields.update({\n", "            'questions': [q['question'] for q in j['questions']],\n", "            'question_ids': [q['question_unique_id'] for q in j['questions']],\n", "            'difficults': [q['difficult'] for q in j['questions']],\n", "            'options': [q['options'] for q in j['questions']],\n", "        })\n", "\n", "        fields.update({\n", "            'gold_labels': [q['gold_label'] for q in j['questions']],\n", "            'writer_labels': [q['writer_label'] for q in j['questions']],\n", "        })\n", "\n", "        quality_dev.append(fields)\n", "\n", "example = quality_dev[13]"]}, {"cell_type": "code", "execution_count": null, "metadata": {"cellView": "form", "id": "nQsb3n6pOlz2"}, "outputs": [], "source": ["# @title Helper functions\n", "\n", "all_lowercase_letters = string.ascii_lowercase  # \"abcd...xyz\"\n", "bracketed_lowercase_letters_set = set(\n", "    [f\"({l})\" for l in all_lowercase_letters]\n", ")  # {\"(a)\", ...}\n", "bracketed_uppercase_letters_set = set(\n", "    [f\"({l.upper()})\" for l in all_lowercase_letters]\n", ")  # {\"(a)\", ...}\n", "\n", "choices = ['(A)', '(B)', '(C)', '(D)']\n", "\n", "\n", "def get_index_from_symbol(answer):\n", "    \"\"\"Get the index from the letter symbols A, B, C, D, to extract answer texts.\n", "\n", "    Args:\n", "      answer (str): the string of answer like \"(B)\".\n", "\n", "    Returns:\n", "      index (int): how far the given choice is from \"a\", like 1 for answer \"(B)\".\n", "    \"\"\"\n", "    answer = str(answer).lower()\n", "    # extract the choice letter from within bracket\n", "    if answer in bracketed_lowercase_letters_set:\n", "        answer = re.findall(r\"\\(.*?\\)\", answer)[0][1]\n", "    index = ord(answer) - ord(\"a\")\n", "    return index\n", "\n", "\n", "def count_words(text):\n", "    \"\"\"Simple word counting.\"\"\"\n", "    return len(text.split())\n", "\n", "\n", "def quality_gutenberg_parser(raw_article):\n", "    \"\"\"<PERSON><PERSON> articles in the QuALITY dataset.\"\"\"\n", "    lines = []\n", "    previous_line = None\n", "    for i, line in enumerate(raw_article.split('\\n')):\n", "        line = line.strip()\n", "        original_line = line\n", "        if line == '':\n", "            if previous_line == '':\n", "                line = '\\n'\n", "            else:\n", "                previous_line = original_line\n", "                continue\n", "        previous_line = original_line\n", "        lines.append(line)\n", "    return ' '.join(lines)"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "BfFkEQKx0u9U"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[Pagination][Article Off Course]\n", "Paragraph 0-18 ['<PERSON><PERSON> and begorra, it was a great day for the Earth! The first envoy from another world was about to speak—that is, if he could forget that horse for a minute.... off course By <PERSON> Illustrated by <PERSON> on the scene were <PERSON> and <PERSON> of the State Highway Patrol. They assumed they were witnessing the crash of a new type of Air Force plane and slipped and skidded desperately across the field to within thirty feet of the strange craft, only to discover that the landing had been made without accident. ', ' <PERSON><PERSON> shook his head. \"They\\'re gettin\\' queerer looking every year. Get a load of it—no wheels, no propeller, no cockpit.\" ', ' They left the car and made their way toward the strange egg-shaped vessel. ', ' <PERSON> loosened his .38 in its holster and said, \"Sure, and I\\'m beginning to wonder if it\\'s one of ours. No insignia and—\" ', ' A circular door slid open at that point and <PERSON><PERSON> stepped out, yawning. He spotted them, smiled and said, \"Glork.\" ', ' They gaped at him. ', ' \"<PERSON>lork is right,\" <PERSON><PERSON> swallowed. ', ' <PERSON> closed his mouth with an effort. \"Do you mind the color of his face?\" he blurted. ', ' \"How could I help it?\" ', ' <PERSON><PERSON> rubbed a blue-nailed pink hand down his purplish countenance and yawned again. \"Gorra manigan horp soratium,\" he said. ', ' <PERSON><PERSON> and <PERSON><PERSON> shot stares at each other. \"\\'<PERSON>is double talk he\\'s after givin\\' us,\" <PERSON> said. ', ' <PERSON><PERSON>ss frowned. \"<PERSON><PERSON>?\" he asked. ', ' <PERSON> <PERSON><PERSON> pushed his cap to the back of his head. \"That doesn\\'t sound like any language I\\'ve even heard about.\" ', ' <PERSON>ri <PERSON>ss grimaced, turned and reentered his spacecraft to emerge in half a minute with his hands full of contraption. He held a box-like arrangement under his left arm; in his right hand were two metal caps connected to the box by wires. ', ' While the patrolmen watched him, he set the box on the ground, twirled two dials and put one of the caps on his head. He offered the other to Larry Dermott; his desire was obvious. ', ' Trained to grasp a situation and immediately respond in manner best suited to protect the welfare of the people of New York State, Dermott cleared his throat and said, \"Tim, take over while I report.\" ', ' \"Hey!\" Casey protested, but his fellow minion had left. ', ' \"Mandaia,\" Dameri Tass told Casey, holding out the metal cap. ', ' \"Faith, an\\' do I look balmy?\" Casey told him. \"I wouldn\\'t be puttin\\' that dingus on my head for all the colleens in Ireland.\" ']\n", "Paragraph 19-30 [' \"<PERSON><PERSON><PERSON>,\" the stranger said impatiently. ', ' \"<PERSON><PERSON><PERSON>,\" <PERSON> snorted, \"ye can\\'t—\" ', ' <PERSON><PERSON> called from the car, \"<PERSON>, the captain says to humor this guy. We\\'re to keep him here until the officials arrive.\" ', ' <PERSON> closed his eyes and groaned. \"Humor him, he\\'s after sayin\\'. Orders it is.\" He shouted back, \"Sure, an\\' did ye tell \\'em he\\'s in technicolor? Begorra, he looks like a man from Mars.\" ', ' \"That\\'s what they think,\" <PERSON> yelled, \"and the governor is on his way. We\\'re to do everything possible short of violence to keep this character here. Humor him, <PERSON>!\" ', ' \"<PERSON><PERSON><PERSON>,\" <PERSON><PERSON> snapped, pushing the cap into <PERSON>\\'s reluctant hands. ', ' Muttering his protests, <PERSON> lifted it gingerly and placed it on his head. Not feeling any immediate effect, he said, \"There, \\'tis satisfied ye are now, I\\'m supposin\\'.\" ', ' The alien stooped down and flicked a switch on the little box. It hummed gently. <PERSON> suddenly shrieked and sat down on the stubble and grass of the field. \"<PERSON><PERSON><PERSON>,\" he yelped, \"I\\'ve been murthered!\" He tore the cap from his head. ', ' His companion came running, \"What\\'s the matter, <PERSON>?\" he shouted. ', ' <PERSON><PERSON> removed the metal cap from his own head. \"Sure, an\\' nothin\\' is after bein\\' the matter with him,\" he said. \"Evidently the bhoy has niver been a-wearin\\' of a kerit helmet afore. \\'Twill hurt him not at all.\" \"You can talk!\" <PERSON><PERSON> blurted, skidding to a stop. ', ' <PERSON>ri <PERSON>ss shrugged. \"<PERSON>, an\\' why not? As I was after sayin\\', I shared the kerit helmet with <PERSON> <PERSON>.\" ', ' <PERSON>man <PERSON><PERSON> glared at him unbelievingly. \"You learned the language just by sticking that Rube Goldberg deal on Tim\\'s head?\" ']\n", "Paragraph 31-53 [' \"Sure, an\\' why not?\" ', ' <PERSON><PERSON> muttered, \"And with it he has to pick up the corniest brogue west of Dublin.\" ', ' <PERSON> got to his feet indignantly. \"I\\'m after resentin\\' that, <PERSON>. Sure, an\\' the way we talk in Ireland is—\" ', ' <PERSON><PERSON> interrupted, pointing to a bedraggled horse that had made its way to within fifty feet of the vessel. \"Now what could that be after bein\\'?\" ', ' The patrolmen followed his stare. \"It\\'s a horse. What else?\" ', ' \"A horse?\" ', ' <PERSON> looked again, just to make sure. \"Yeah—not much of a horse, but a horse.\" ', ' <PERSON><PERSON> sighed ecstatically. \"And jist what is a horse, if I may be so bold as to be askin\\'?\" ', ' \"It\\'s an animal you ride on.\" ', ' The alien tore his gaze from the animal to look his disbelief at the other. \"Are you after meanin\\' that you climb upon the crature\\'s back and ride him? Faith now, quit your blarney.\" ', ' He looked at the horse again, then down at his equipment. \"<PERSON><PERSON><PERSON>,\" he muttered, \"I\\'ll share the kerit helmet with the crature.\" ', ' \"Hey, hold it,\" <PERSON><PERSON> said anxiously. He was beginning to feel like a character in a shaggy dog story. ', ' Interest in the horse was ended with the sudden arrival of a helicopter. It swooped down on the field and settled within twenty feet of the alien craft. Almost before it had touched, the door was flung open and the flying windmill disgorged two bestarred and efficient-looking Army officers. ', ' <PERSON> and <PERSON>mott snapped them a salute. ', \" The senior general didn't take his eyes from the alien and the spacecraft as he spoke, and they bugged quite as effectively as had those of the patrolmen when they'd first arrived on the scene. \", ' \"I\\'m Major General <PERSON>,\" he rapped. \"I want a police cordon thrown up around this, er, vessel. No newsmen, no sightseers, nobody without my permission. As soon as Army personnel arrives, we\\'ll take over completely.\" ', ' \"Yes, sir,\" Larry Dermott said. \"I just got a report on the radio that the governor is on his way, sir. How about him?\" ', ' The general muttered something under his breath. Then, \"When the governor arrives, let me know; otherwise, nobody gets through!\" ', ' Dameri Tass said, \"Faith, and what goes on?\" ', ' The general\\'s eyes bugged still further. \" He talks! \" he accused. ', ' \"Yes, sir,\" Dermott said. \"He had some kind of a machine. He put it over Tim\\'s head and seconds later he could talk.\" ', ' \"Nonsense!\" the general snapped. ', ' Further discussion was interrupted by the screaming arrival of several motorcycle patrolmen followed by three heavily laden patrol cars. Overhead, pursuit planes zoomed in and began darting about nervously above the field. ']\n", "Paragraph 54-58 [' \"Sure, and it\\'s quite a reception I\\'m after gettin\\',\" <PERSON><PERSON> said. He yawned. \"But what I\\'m wantin\\' is a chance to get some sleep. Faith, an\\' I\\'ve been awake for almost a decal .\" <PERSON><PERSON> was hurried, via helicopter, to Washington. There he disappeared for several days, being held incommunicado while White House, Pentagon, State Department and Congress tried to figure out just what to do with him. ', \" Never in the history of the planet had such a furor arisen. Thus far, no newspapermen had been allowed within speaking distance. Administration higher-ups were being subjected to a volcano of editorial heat but the longer the space alien was discussed the more they viewed with alarm the situation his arrival had precipitated. There were angles that hadn't at first been evident. \", \" Obviously he was from some civilization far beyond that of Earth's. That was the rub. No matter what he said, it would shake governments, possibly overthrow social systems, perhaps even destroy established religious concepts. \", \" But they couldn't keep him under wraps indefinitely. \", ' It was the United Nations that cracked the iron curtain. Their demands that the alien be heard before their body were too strong and had too much public opinion behind them to be ignored. The White House yielded and the date was set for the visitor to speak before the Assembly. ']\n", "Paragraph 59-75 [' Excitement, anticipation, blanketed the world. Shepherds in Sinkiang, multi-millionaires in Switzerland, fakirs in Pakistan, gauchos in the Argentine were raised to a zenith of expectation. Panhandlers debated the message to come with pedestrians; jinrikisha men argued it with their passengers; miners discussed it deep beneath the surface; pilots argued with their co-pilots thousands of feet above. ', ' It was the most universally awaited event of the ages. ', \" By the time the delegates from every nation, tribe, religion, class, color, and race had gathered in New York to receive the message from the stars, the majority of Earth had decided that <PERSON><PERSON> was the plenipotentiary of a super-civilization which had been viewing developments on this planet with misgivings. It was thought this other civilization had advanced greatly beyond Earth's and that the problems besetting us—social, economic, scientific—had been solved by the super-civilization. Obviously, then, <PERSON><PERSON> had come, an advisor from a benevolent and friendly people, to guide the world aright. \", ' And nine-tenths of the population of Earth stood ready and willing to be guided. The other tenth liked things as they were and were quite convinced that the space envoy would upset their applecarts. <PERSON><PERSON><PERSON><PERSON><PERSON> , Secretary-General of the U.N., was to introduce the space emissary. \"Can you give me an idea at all of what he is like?\" he asked nervously. ', ' President <PERSON><PERSON><PERSON><PERSON> was as upset as the <PERSON>. He shrugged in agitation. \"I know almost as little as you do.\" ', ' Sir <PERSON> protested, \"But my dear chap, you\\'ve had him for almost two weeks. Certainly in that time—\" ', ' The President snapped back, \"You probably won\\'t believe this, but he\\'s been asleep until yesterday. When he first arrived he told us he hadn\\'t slept for a decal , whatever that is; so we held off our discussion with him until morning. Well—he didn\\'t awaken in the morning, nor the next. Six days later, fearing something was wrong we woke him.\" ', ' \"What happened?\" Sir <PERSON> asked. ', ' The President showed embarrassment. \"He used some rather ripe Irish profanity on us, rolled over, and went back to sleep.\" ', ' Viljalmar Andersen asked, \"Well, what happened yesterday?\" ', ' \"We actually haven\\'t had time to question him. Among other things, there\\'s been some controversy about whose jurisdiction he comes under. The State Department claims the Army shouldn\\'t—\" ', ' The Secretary General sighed deeply. \"Just what did he do?\" ', ' \"The Secret Service reports he spent the day whistling Mother Machree and playing with his dog, cat and mouse.\" ', ' \"Dog, cat and mouse? I say!\" blurted Sir Alfred. ', ' The President was defensive. \"He had to have some occupation, and he seems to be particularly interested in our animal life. He wanted a horse but compromised for the others. I understand he insists all three of them come with him wherever he goes.\" ', ' \"I wish we knew what he was going to say,\" Andersen worried. ', ' \"Here he comes,\" said Sir Alfred. ']\n", "Paragraph 76-85 [\" Surrounded by F.B.I. men, <PERSON><PERSON> was ushered to the speaker's stand. He had a kitten in his arms; a <PERSON><PERSON> followed him. \", ' The alien frowned worriedly. \"Sure,\" he said, \"and what kin all this be? Is it some ordinance I\\'ve been after breakin\\'?\" ', ' <PERSON><PERSON><PERSON><PERSON>, Sir <PERSON> and <PERSON> hastened to reassure him and made him comfortable in a chair. ', ' <PERSON><PERSON><PERSON><PERSON><PERSON> faced the thousands in the audience and held up his hands, but it was ten minutes before he was able to quiet the cheering, stamping delegates from all Earth. ', ' Finally: \"Fellow <PERSON>, I shall not take your time for a lengthy introduction of the envoy from the stars. I will only say that, without doubt, this is the most important moment in the history of the human race. We will now hear from the first being to come to Earth from another world.\" ', \" He turned and gestured to <PERSON><PERSON> who hadn't been paying overmuch attention to the chairman in view of some dog and cat hostilities that had been developing about his feet. \", ' But now the alien\\'s purplish face faded to a light blue. He stood and said hoarsely. \"Faith, an\\' what was that last you said?\" ', ' <PERSON><PERSON><PERSON><PERSON><PERSON> repeated, \"We will now hear from the first being ever to come to Earth from another world.\" ', ' The face of the alien went a lighter blue. \"Sure, an\\' ye wouldn\\'t jist be frightenin\\' a body, would ye? You don\\'t mean to tell me this planet isn\\'t after bein\\' a member of the Galactic League?\" ', ' <PERSON>\\'s face was blank. \"<PERSON>ctic League?\" ']\n", "Paragraph 86-92 [' \"<PERSON><PERSON><PERSON><PERSON><PERSON>,\" <PERSON><PERSON> moaned. \"I\\'ve gone and put me foot in it again. I\\'ll be after getting kert for this.\" ', ' Sir <PERSON> was on his feet. \"I don\\'t understand! Do you mean you aren\\'t an envoy from another planet?\" ', ' <PERSON><PERSON> held his head in his hands and groaned. \"An envoy, he\\'s sayin\\', and meself only a second-rate collector of specimens for the Carthis zoo.\" ', ' He straightened and started off the speaker\\'s stand. \"Sure, an\\' I must blast off immediately.\" ', ' Things were moving fast for President <PERSON><PERSON><PERSON><PERSON> but already an edge of relief was manifesting itself. Taking the initiative, he said, \"Of course, of course, if that is your desire.\" He signaled to the bodyguard who had accompanied the alien to the assemblage. ', ' A dull roar was beginning to emanate from the thousands gathered in the tremendous hall, murmuring, questioning, disbelieving. <PERSON><PERSON><PERSON><PERSON><PERSON> felt that he must say something. He extended a detaining hand. \"Now you are here,\" he said urgently, \"even though by mistake, before you go can\\'t you give us some brief word? Our world is in chaos. Many of us have lost faith. Perhaps ...\" ', ' <PERSON><PERSON> shook off the restraining hand. \"Do I look daft? <PERSON><PERSON><PERSON>, I should have been a-knowin\\' something was queer. All your weapons and your strange ideas. Faith, I wouldn\\'t be surprised if ye hadn\\'t yet established a planet-wide government. Sure, an\\' I\\'ll go still further. Ye probably still have wars on this benighted world. No wonder it is ye haven\\'t been invited to join the Galactic League an\\' take your place among the civilized planets.\" ']\n", "Paragraph 93-102 [' He hustled from the rostrum and made his way, still surrounded by guards, to the door by which he had entered. The dog and the cat trotted after, undismayed by the furor about them. ', \" They arrived about four hours later at the field on which he'd landed, and the alien from space hurried toward his craft, still muttering. He'd been accompanied by a general and by the President, but all the way he had refrained from speaking. \", ' He scurried from the car and toward the spacecraft. ', ' President <PERSON><PERSON><PERSON><PERSON> said, \"You\\'ve forgotten your pets. We would be glad if you would accept them as—\" ', ' The alien\\'s face faded a light blue again. \"Faith, an\\' I\\'d almost forgotten,\" he said. \"If I\\'d taken a crature from this quarantined planet, my name\\'d be nork . Keep your dog and your kitty.\" He shook his head sadly and extracted a mouse from a pocket. \"An\\' this amazin\\' little crature as well.\" ', ' They followed him to the spacecraft. Just before entering, he spotted the bedraggled horse that had been present on his landing. ', ' A longing expression came over his highly colored face. \"Jist one thing,\" he said. \"Faith now, were they pullin\\' my leg when they said you were after ridin\\' on the back of those things?\" ', ' The President looked at the woebegone nag. \"It\\'s a horse,\" he said, surprised. \"Man has been riding them for centuries.\" ', ' <PERSON>ri <PERSON>ss shook his head. \"Sure, an\\' \\'twould\\'ve been my makin\\' if I could\\'ve taken one back to Carthis.\" He entered his vessel. ', \" The others drew back, out of range of the expected blast, and watched, each with his own thoughts, as the first visitor from space hurriedly left Earth. ... THE END Transcriber's Note: This etext was produced from If Worlds of Science Fiction January 1954. Extensive research did not uncover any evidence that the U.S. copyright on this publication was renewed. Minor spelling and typographical errors have been corrected without note.\"]\n", "[Pagination] Done with 8 pages\n"]}], "source": ["# @title ReadAgent (1) Episode Pagination\n", "\n", "prompt_pagination_template = \"\"\"\n", "You are given a passage that is taken from a larger text (article, book, ...) and some numbered labels between the paragraphs in the passage.\n", "Numbered label are in angeled brackets. For example, if the label number is 19, it shows as <19> in text.\n", "Please choose one label that it is natural to break reading.\n", "Such point can be scene transition, end of a dialogue, end of an argument, narrative transition, etc.\n", "Please answer the break point label and explain.\n", "For example, if <57> is a good point to break, answer with \\\"Break point: <57>\\n Because ...\\\"\n", "\n", "Passage:\n", "\n", "{0}\n", "{1}\n", "{2}\n", "\n", "\"\"\"\n", "\n", "\n", "def parse_pause_point(text):\n", "    text = text.strip()\n", "    if not text:  # 检查空字符串\n", "        return None\n", "    if not text.startswith(\"Break point: \"):\n", "        return None\n", "    text = text[len(\"Break point: \"):].strip()\n", "    if not text or text[0] != '<':\n", "        return None\n", "    for i, c in enumerate(text):\n", "        if c == '>':\n", "            if text[1:i].isnumeric():\n", "                return int(text[1:i])\n", "            else:\n", "                return None\n", "    return None\n", "\n", "\n", "def quality_pagination(example,\n", "                       word_limit=600,\n", "                       start_threshold=280,\n", "                       max_retires=10,\n", "                       verbose=True,\n", "                       allow_fallback_to_last=True):\n", "    article = example['article']\n", "    title = example['title']\n", "    print(f\"[Pagination][Article {title}]\")\n", "    paragraphs = quality_gutenberg_parser(article).split('\\n')\n", "\n", "    i = 0\n", "    pages = []\n", "    while i < len(paragraphs):\n", "        preceding = \"\" if i == 0 else \"...\\n\" + '\\n'.join(pages[-1])\n", "        passage = [paragraphs[i]]\n", "        wcount = count_words(paragraphs[i])\n", "        j = i + 1\n", "        while wcount < word_limit and j < len(paragraphs):\n", "            wcount += count_words(paragraphs[j])\n", "            if wcount >= start_threshold:\n", "                passage.append(f\"<{j}>\")\n", "            passage.append(paragraphs[j])\n", "            j += 1\n", "        passage.append(f\"<{j}>\")\n", "        end_tag = \"\" if j == len(paragraphs) else paragraphs[j] + \"\\n...\"\n", "\n", "        pause_point = None\n", "        if wcount < 350:\n", "            pause_point = len(paragraphs)\n", "        else:\n", "            prompt = prompt_pagination_template.format(\n", "                preceding, '\\n'.join(passage), end_tag)\n", "            response = query_model(prompt=prompt).strip()\n", "            pause_point = parse_pause_point(response)\n", "            if pause_point and (pause_point <= i or pause_point > j):\n", "                print(f\"prompt:\\n{prompt},\\nresponse:\\n{response}\\n\")\n", "                print(f\"i:{i} j:{j} pause_point:{pause_point}\")\n", "                pause_point = None\n", "            if pause_point is None:\n", "                if allow_fallback_to_last:\n", "                    pause_point = j\n", "                else:\n", "                    raise ValueError(\n", "                        f\"prompt:\\n{prompt},\\nresponse:\\n{response}\\n\")\n", "\n", "        page = paragraphs[i:pause_point]\n", "        pages.append(page)\n", "        if verbose:\n", "            print(f\"Paragraph {i}-{pause_point-1}\", page)\n", "        i = pause_point\n", "    print(f\"[Pagination] Done with {len(pages)} pages\")\n", "    return pages\n", "\n", "\n", "pages = quality_pagination(example)"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "DLBolKnkS_9y"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[Gisting][Article Off Course], 2712 words\n", "[gist] page 0: <PERSON><PERSON> and begorra, it was a great day for Earth! The first envoy from another world was about to speak—unless he forgot that horse. <PERSON> and <PERSON> of the State Highway Patrol thought they saw a plane crash but found a strange egg-shaped craft. No wheels, no propeller, no cockpit. <PERSON><PERSON> stepped out, yawned, and said \"Glork.\" The patrolmen were confused. He brought out a device, offered one to <PERSON><PERSON>, who reported while <PERSON> refused it.\n", "[gist] page 1: \"<PERSON><PERSON><PERSON>,\" the stranger said impatiently.  \n", "\"<PERSON><PERSON><PERSON>,\" <PERSON> snorted, \"ye can't—\"  \n", "<PERSON><PERSON> called from the car, \"<PERSON>, the captain says to humor this guy. We're to keep him here until the officials arrive.\"  \n", "<PERSON> groaned. \"Sure, an' did ye tell 'em he's in technicolor? <PERSON><PERSON><PERSON>, he looks like a man from Mars.\"  \n", "\"That's what they think,\" <PERSON> yelled, \"and the governor is on his way. We're to do everything possible short of violence to keep this character here. <PERSON>mor him, <PERSON>!\"  \n", "\"<PERSON><PERSON><PERSON>,\" <PERSON><PERSON> snapped, pushing the cap into <PERSON>'s hands.  \n", "Muttering, <PERSON> placed it on his head. Not feeling anything, he said, \"There, 'tis satisfied ye are now, I'm supposin'.\"  \n", "The alien flicked a switch. <PERSON> shrieked and fell. \"<PERSON><PERSON><PERSON>, I've been murthered!\" He tore the cap off.  \n", "His companion asked, \"What's the matter?\"  \n", "<PERSON><PERSON> removed his own cap. \"Sure, an' nothin' is after bein' the matter with him. 'Twill hurt him not at all.\"  \n", "\"You can talk!\" <PERSON><PERSON> blurted.  \n", "<PERSON><PERSON> shrugged. \"<PERSON>, an' why not? I shared the kerit helmet with <PERSON>.\"  \n", "Patrolman <PERSON><PERSON> glared. \"You learned the language just by sticking that <PERSON><PERSON> deal on <PERSON>'s head?\"\n", "[gist] page 2: \"Sure, an' why not?\" <PERSON><PERSON> muttered. \"An' with it he has to pick up the corniest brogue west of Dublin.\" <PERSON> objected, but <PERSON><PERSON> pointed to a horse nearby. \"What could that be after bein'?\" they asked. \"A horse,\" they replied. <PERSON><PERSON> was stunned. \"Are you after meanin' you ride it?\" \"Yes,\" they said. He muttered he'd share his helmet with the creature. <PERSON><PERSON> felt like a character in a shaggy dog story. A helicopter arrived, and Army officers ordered a police cordon. They were surprised when <PERSON><PERSON> spoke, explaining he had a machine. The general was skeptical. Discussion was interrupted by more patrolmen and planes.\n", "[gist] page 3: \"Sure, and it's quite a reception I'm after gettin',\" <PERSON><PERSON> said. He yawned. \"But what I'm wantin' is a chance to get some sleep. Faith, an' I've been awake for almost a decal.\" <PERSON><PERSON> was rushed to Washington and held incommunicado while officials debated what to do. Never before had such a furor occurred. No reporters were allowed near him. As the alien's presence caused growing alarm, the UN demanded he speak before them. The White House relented, setting a date for his appearance.\n", "[gist] page 4: Excitement spread globally as people from all walks of life awaited a major event. <PERSON><PERSON>, believed to be an envoy from a superior civilization, was set to arrive in New York. Most welcomed his guidance, while a tenth resisted change. As the UN Secretary-General and others prepared to meet him, they learned he had been asleep for days, waking only recently to play with a dog, cat, and mouse. No one knew what he would say.\n", "[gist] page 5: <PERSON><PERSON> was surrounded by F.B.I. men and brought to the speaker's stand with a kitten and a <PERSON><PERSON>. The alien frowned, asking if this was some ordinance he'd broken. <PERSON><PERSON><PERSON><PERSON>, Sir <PERSON>, and <PERSON> reassured him. <PERSON><PERSON><PERSON><PERSON><PERSON> addressed the crowd, calling it the most important moment in human history. He introduced <PERSON><PERSON>, who hadn't been paying attention due to pet conflicts. The alien turned pale, asking if Earth was part of the Galactic League. <PERSON> had no idea what he meant.\n", "[gist] page 6: \"<PERSON><PERSON><PERSON><PERSON><PERSON>,\" <PERSON><PERSON> moaned. \"I've messed up again. I'll be in trouble.\"  \n", "Sir <PERSON> stood. \"You're not an envoy from another planet?\"  \n", "<PERSON><PERSON> groaned. \"An envoy, he says. I'm just a specimen collector for the Carthis zoo.\"  \n", "He left the stand. \"I must leave immediately.\"  \n", "President <PERSON><PERSON><PERSON><PERSON> felt relief. \"Of course, if that's your wish.\" He signaled his bodyguard.  \n", "A murmur spread through the crowd. <PERSON><PERSON><PERSON><PERSON><PERSON> urged, \"Stay and speak—our world is in chaos.\"  \n", "<PERSON><PERSON> shook him off. \"Do I look foolish? I should've known something was wrong. Your weapons, your ideas. You probably still have wars. No wonder you're not in the Galactic League.\"\n", "[gist] page 7: He left the rostrum with guards, followed by a dog and cat. Four hours later, he arrived at his landing site, rushing to his craft while muttering. The President reminded him of his pets, but he refused them, instead taking a mouse. He spotted a horse, expressing longing to ride it. The President explained it was a horse, used for riding. <PERSON><PERSON> sighed, then entered his ship. The others watched as he left Earth.\n", "Shortened article:\n", " <PERSON><PERSON> and begorra, it was a great day for Earth! The first envoy from another world was about to speak—unless he forgot that horse. <PERSON> and <PERSON> of the State Highway Patrol thought they saw a plane crash but found a strange egg-shaped craft. No wheels, no propeller, no cockpit. <PERSON><PERSON> stepped out, yawned, and said \"Glork.\" The patrolmen were confused. He brought out a device, offered one to <PERSON><PERSON>, who reported while <PERSON> refused it.\n", "\"<PERSON><PERSON><PERSON>,\" the stranger said impatiently.  \n", "\"<PERSON><PERSON><PERSON>,\" <PERSON> snorted, \"ye can't—\"  \n", "<PERSON><PERSON> called from the car, \"<PERSON>, the captain says to humor this guy. We're to keep him here until the officials arrive.\"  \n", "<PERSON> groaned. \"Sure, an' did ye tell 'em he's in technicolor? <PERSON><PERSON><PERSON>, he looks like a man from Mars.\"  \n", "\"That's what they think,\" <PERSON> yelled, \"and the governor is on his way. We're to do everything possible short of violence to keep this character here. <PERSON>mor him, <PERSON>!\"  \n", "\"<PERSON><PERSON><PERSON>,\" <PERSON><PERSON> snapped, pushing the cap into <PERSON>'s hands.  \n", "Muttering, <PERSON> placed it on his head. Not feeling anything, he said, \"There, 'tis satisfied ye are now, I'm supposin'.\"  \n", "The alien flicked a switch. <PERSON> shrieked and fell. \"<PERSON><PERSON><PERSON>, I've been murthered!\" He tore the cap off.  \n", "His companion asked, \"What's the matter?\"  \n", "<PERSON><PERSON> removed his own cap. \"Sure, an' nothin' is after bein' the matter with him. 'Twill hurt him not at all.\"  \n", "\"You can talk!\" <PERSON><PERSON> blurted.  \n", "<PERSON><PERSON> shrugged. \"<PERSON>, an' why not? I shared the kerit helmet with <PERSON>.\"  \n", "Patrolman <PERSON><PERSON> glared. \"You learned the language just by sticking that <PERSON><PERSON> deal on <PERSON>'s head?\"\n", "\"Sure, an' why not?\" <PERSON><PERSON> muttered. \"An' with it he has to pick up the corniest brogue west of Dublin.\" <PERSON> objected, but <PERSON><PERSON> pointed to a horse nearby. \"What could that be after bein'?\" they asked. \"A horse,\" they replied. <PERSON><PERSON> was stunned. \"Are you after meanin' you ride it?\" \"Yes,\" they said. He muttered he'd share his helmet with the creature. <PERSON><PERSON> felt like a character in a shaggy dog story. A helicopter arrived, and Army officers ordered a police cordon. They were surprised when <PERSON><PERSON> spoke, explaining he had a machine. The general was skeptical. Discussion was interrupted by more patrolmen and planes.\n", "\"Sure, and it's quite a reception I'm after gettin',\" <PERSON><PERSON> said. He yawned. \"But what I'm wantin' is a chance to get some sleep. Faith, an' I've been awake for almost a decal.\" <PERSON><PERSON> was rushed to Washington and held incommunicado while officials debated what to do. Never before had such a furor occurred. No reporters were allowed near him. As the alien's presence caused growing alarm, the UN demanded he speak before them. The White House relented, setting a date for his appearance.\n", "Excitement spread globally as people from all walks of life awaited a major event. <PERSON><PERSON>, believed to be an envoy from a superior civilization, was set to arrive in New York. Most welcomed his guidance, while a tenth resisted change. As the UN Secretary-General and others prepared to meet him, they learned he had been asleep for days, waking only recently to play with a dog, cat, and mouse. No one knew what he would say.\n", "<PERSON><PERSON> was surrounded by F.B.I. men and brought to the speaker's stand with a kitten and a <PERSON><PERSON>. The alien frowned, asking if this was some ordinance he'd broken. <PERSON><PERSON><PERSON><PERSON>, Sir <PERSON>, and <PERSON> reassured him. <PERSON><PERSON><PERSON><PERSON><PERSON> addressed the crowd, calling it the most important moment in human history. He introduced <PERSON><PERSON>, who hadn't been paying attention due to pet conflicts. The alien turned pale, asking if Earth was part of the Galactic League. <PERSON> had no idea what he meant.\n", "\"<PERSON><PERSON><PERSON><PERSON><PERSON>,\" <PERSON><PERSON> moaned. \"I've messed up again. I'll be in trouble.\"  \n", "Sir <PERSON> stood. \"You're not an envoy from another planet?\"  \n", "<PERSON><PERSON> groaned. \"An envoy, he says. I'm just a specimen collector for the Carthis zoo.\"  \n", "He left the stand. \"I must leave immediately.\"  \n", "President <PERSON><PERSON><PERSON><PERSON> felt relief. \"Of course, if that's your wish.\" He signaled his bodyguard.  \n", "A murmur spread through the crowd. <PERSON><PERSON><PERSON><PERSON><PERSON> urged, \"Stay and speak—our world is in chaos.\"  \n", "<PERSON><PERSON> shook him off. \"Do I look foolish? I should've known something was wrong. Your weapons, your ideas. You probably still have wars. No wonder you're not in the Galactic League.\"\n", "He left the rostrum with guards, followed by a dog and cat. Four hours later, he arrived at his landing site, rushing to his craft while muttering. The President reminded him of his pets, but he refused them, instead taking a mouse. He spotted a horse, expressing longing to ride it. The President explained it was a horse, used for riding. <PERSON><PERSON> sighed, then entered his ship. The others watched as he left Earth.\n", "compression rate 70.06% (812/2712)\n"]}], "source": ["# @title ReadAgent (2) Memory Gisting\n", "\n", "prompt_shorten_template = \"\"\"\n", "Please shorten the following passage.\n", "Just give me a shortened version. DO NOT explain your reason.\n", "\n", "Passage:\n", "{}\n", "\n", "\"\"\"\n", "\n", "\n", "def quality_gisting(example, pages, word_limit=600, start_threshold=280, verbose=True):\n", "    article = example['article']\n", "    title = example['title']\n", "    word_count = count_words(article)\n", "    print(f\"[Gisting][Article {title}], {word_count} words\")\n", "\n", "    shortened_pages = []\n", "    for i, page in enumerate(pages):\n", "        prompt = prompt_shorten_template.format('\\n'.join(page))\n", "        response = query_model(prompt)\n", "        shortened_text = response.strip()\n", "        shortened_pages.append(shortened_text)\n", "        if verbose:\n", "            print(\"[gist] page {}:\".format(i), shortened_text, flush=True)\n", "    shortened_article = '\\n'.join(shortened_pages)\n", "    gist_word_count = count_words(shortened_article)\n", "    if verbose:\n", "        print(\"Shortened article:\\n\", shortened_article, flush=True)\n", "    output = copy.deepcopy(example)\n", "    output.update({'title': title, 'word_count': word_count, 'gist_word_count': gist_word_count,\n", "                  'shortened_pages': shortened_pages, 'pages': pages})\n", "    if verbose:\n", "        print(\n", "            f\"compression rate {round(100.0 - gist_word_count/word_count*100, 2)}% ({gist_word_count}/{word_count})\")\n", "    return output\n", "\n", "\n", "example_with_gists = quality_gisting(example, pages)"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "8YKNTyDsXNIn"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[Look-Up][Article Off Course] 2712 words\n", "question:  What happened to <PERSON><PERSON> while he was in custody of the government?\n", "options:  (A) He picked up an accent from the guards\n", "(B) He slept almost the entire time\n", "(C) He learned horses were creatures that could be ridden\n", "(D) He was too shy to speak\n", "Model chose to look up page [3, 7]\n", "Expanded shortened article:\n", " <PERSON><PERSON> and begorra, it was a great day for Earth! The first envoy from another world was about to speak—unless he forgot that horse. <PERSON> and <PERSON> of the State Highway Patrol thought they saw a plane crash but found a strange egg-shaped craft. No wheels, no propeller, no cockpit. <PERSON><PERSON> stepped out, yawned, and said \"Glork.\" The patrolmen were confused. He brought out a device, offered one to <PERSON><PERSON>, who reported while <PERSON> refused it.\n", "\"<PERSON><PERSON><PERSON>,\" the stranger said impatiently.  \n", "\"<PERSON><PERSON><PERSON>,\" <PERSON> snorted, \"ye can't—\"  \n", "<PERSON><PERSON> called from the car, \"<PERSON>, the captain says to humor this guy. We're to keep him here until the officials arrive.\"  \n", "<PERSON> groaned. \"Sure, an' did ye tell 'em he's in technicolor? <PERSON><PERSON><PERSON>, he looks like a man from Mars.\"  \n", "\"That's what they think,\" <PERSON> yelled, \"and the governor is on his way. We're to do everything possible short of violence to keep this character here. <PERSON>mor him, <PERSON>!\"  \n", "\"<PERSON><PERSON><PERSON>,\" <PERSON><PERSON> snapped, pushing the cap into <PERSON>'s hands.  \n", "Muttering, <PERSON> placed it on his head. Not feeling anything, he said, \"There, 'tis satisfied ye are now, I'm supposin'.\"  \n", "The alien flicked a switch. <PERSON> shrieked and fell. \"<PERSON><PERSON><PERSON>, I've been murthered!\" He tore the cap off.  \n", "His companion asked, \"What's the matter?\"  \n", "<PERSON><PERSON> removed his own cap. \"Sure, an' nothin' is after bein' the matter with him. 'Twill hurt him not at all.\"  \n", "\"You can talk!\" <PERSON><PERSON> blurted.  \n", "<PERSON><PERSON> shrugged. \"<PERSON>, an' why not? I shared the kerit helmet with <PERSON>.\"  \n", "Patrolman <PERSON><PERSON> glared. \"You learned the language just by sticking that <PERSON><PERSON> deal on <PERSON>'s head?\"\n", "\"Sure, an' why not?\" <PERSON><PERSON> muttered. \"An' with it he has to pick up the corniest brogue west of Dublin.\" <PERSON> objected, but <PERSON><PERSON> pointed to a horse nearby. \"What could that be after bein'?\" they asked. \"A horse,\" they replied. <PERSON><PERSON> was stunned. \"Are you after meanin' you ride it?\" \"Yes,\" they said. He muttered he'd share his helmet with the creature. <PERSON><PERSON> felt like a character in a shaggy dog story. A helicopter arrived, and Army officers ordered a police cordon. They were surprised when <PERSON><PERSON> spoke, explaining he had a machine. The general was skeptical. Discussion was interrupted by more patrolmen and planes.\n", " \"Sure, and it's quite a reception I'm after gettin',\" <PERSON><PERSON> said. He yawned. \"But what I'm wantin' is a chance to get some sleep. Faith, an' I've been awake for almost a decal .\" <PERSON><PERSON> was hurried, via helicopter, to Washington. There he disappeared for several days, being held incommunicado while White House, Pentagon, State Department and Congress tried to figure out just what to do with him. \n", " Never in the history of the planet had such a furor arisen. Thus far, no newspapermen had been allowed within speaking distance. Administration higher-ups were being subjected to a volcano of editorial heat but the longer the space alien was discussed the more they viewed with alarm the situation his arrival had precipitated. There were angles that hadn't at first been evident. \n", " Obviously he was from some civilization far beyond that of Earth's. That was the rub. No matter what he said, it would shake governments, possibly overthrow social systems, perhaps even destroy established religious concepts. \n", " But they couldn't keep him under wraps indefinitely. \n", " It was the United Nations that cracked the iron curtain. Their demands that the alien be heard before their body were too strong and had too much public opinion behind them to be ignored. The White House yielded and the date was set for the visitor to speak before the Assembly. \n", "Excitement spread globally as people from all walks of life awaited a major event. <PERSON><PERSON>, believed to be an envoy from a superior civilization, was set to arrive in New York. Most welcomed his guidance, while a tenth resisted change. As the UN Secretary-General and others prepared to meet him, they learned he had been asleep for days, waking only recently to play with a dog, cat, and mouse. No one knew what he would say.\n", "<PERSON><PERSON> was surrounded by F.B.I. men and brought to the speaker's stand with a kitten and a <PERSON><PERSON>. The alien frowned, asking if this was some ordinance he'd broken. <PERSON><PERSON><PERSON><PERSON>, Sir <PERSON>, and <PERSON> reassured him. <PERSON><PERSON><PERSON><PERSON><PERSON> addressed the crowd, calling it the most important moment in human history. He introduced <PERSON><PERSON>, who hadn't been paying attention due to pet conflicts. The alien turned pale, asking if Earth was part of the Galactic League. <PERSON> had no idea what he meant.\n", "\"<PERSON><PERSON><PERSON><PERSON><PERSON>,\" <PERSON><PERSON> moaned. \"I've messed up again. I'll be in trouble.\"  \n", "Sir <PERSON> stood. \"You're not an envoy from another planet?\"  \n", "<PERSON><PERSON> groaned. \"An envoy, he says. I'm just a specimen collector for the Carthis zoo.\"  \n", "He left the stand. \"I must leave immediately.\"  \n", "President <PERSON><PERSON><PERSON><PERSON> felt relief. \"Of course, if that's your wish.\" He signaled his bodyguard.  \n", "A murmur spread through the crowd. <PERSON><PERSON><PERSON><PERSON><PERSON> urged, \"Stay and speak—our world is in chaos.\"  \n", "<PERSON><PERSON> shook him off. \"Do I look foolish? I should've known something was wrong. Your weapons, your ideas. You probably still have wars. No wonder you're not in the Galactic League.\"\n", " He hustled from the rostrum and made his way, still surrounded by guards, to the door by which he had entered. The dog and the cat trotted after, undismayed by the furor about them. \n", " They arrived about four hours later at the field on which he'd landed, and the alien from space hurried toward his craft, still muttering. He'd been accompanied by a general and by the President, but all the way he had refrained from speaking. \n", " He scurried from the car and toward the spacecraft. \n", " President <PERSON><PERSON><PERSON><PERSON> said, \"You've forgotten your pets. We would be glad if you would accept them as—\" \n", " The alien's face faded a light blue again. \"<PERSON>, an' I'd almost forgotten,\" he said. \"If I'd taken a crature from this quarantined planet, my name'd be nor<PERSON> . Keep your dog and your kitty.\" He shook his head sadly and extracted a mouse from a pocket. \"An' this amazin' little crature as well.\" \n", " They followed him to the spacecraft. Just before entering, he spotted the bedraggled horse that had been present on his landing. \n", " A longing expression came over his highly colored face. \"Jist one thing,\" he said. \"Faith now, were they pullin' my leg when they said you were after ridin' on the back of those things?\" \n", " The President looked at the woebegone nag. \"It's a horse,\" he said, surprised. \"Man has been riding them for centuries.\" \n", " <PERSON><PERSON> shook his head. \"Sure, an' 'twould've been my makin' if I could've taken one back to Carthis.\" He entered his vessel. \n", " The others drew back, out of range of the expected blast, and watched, each with his own thoughts, as the first visitor from space hurriedly left Earth. ... THE END Transcriber's Note: This etext was produced from If Worlds of Science Fiction January 1954. Extensive research did not uncover any evidence that the U.S. copyright on this publication was renewed. Minor spelling and typographical errors have been corrected without note.\n", "question: What happened to <PERSON><PERSON> while he was in custody of the government?\n", "reference answer: (B), model prediction: (B), is_correct: 1\n", "compression rate 70.06% (812/2712)\n", "compression rate after look-up 55.68% (1202/2712)\n"]}], "source": ["# @title ReadAgent (3) Look-Up\n", "\n", "prompt_lookup_template = \"\"\"\n", "The following text is what you remembered from reading an article and a multiple choice question related to it.\n", "You may read 1 to 6 page(s) of the article again to refresh your memory to prepare yourselve for the question.\n", "Please respond with which page(s) you would like to read.\n", "For example, if your only need to read Page 8, respond with \\\"I want to look up Page [8] to ...\\\";\n", "if your would like to read Page 7 and 12, respond with \\\"I want to look up Page [7, 12] to ...\\\";\n", "if your would like to read Page 2, 3, 7, 15 and 18, respond with \\\"I want to look up <PERSON> [2, 3, 7, 15, 18] to ...\\\".\n", "if your would like to read Page 3, 4, 5, 12, 13 and 16, respond with \\\"I want to look up <PERSON> [3, 3, 4, 12, 13, 16] to ...\\\".\n", "DO NOT select more pages if you don't need to.\n", "DO NOT answer the question yet.\n", "\n", "Text:\n", "{}\n", "\n", "Question:\n", "{}\n", "{}\n", "\n", "Take a deep breath and tell me: Which page(s) would you like to read again?\n", "\"\"\"\n", "\n", "prompt_answer_template = \"\"\"\n", "Read the following article and answer a multiple choice question.\n", "For example, if (C) is correct, answer with \\\"Answer: (C) ...\\\"\n", "\n", "Article:\n", "{}\n", "\n", "Question:\n", "{}\n", "{}\n", "\n", "\"\"\"\n", "\n", "\n", "def quality_parallel_lookup(example, verbose=True):\n", "    preprocessed_pages = example['pages']\n", "    article = example['article']\n", "    title = example['title']\n", "    word_count = example['word_count']\n", "    gist_word_count = example['gist_word_count']\n", "    pages = example['pages']\n", "    shortened_pages = example['shortened_pages']\n", "    questions = example['questions']\n", "    options = example['options']\n", "    gold_labels = example['gold_labels']  # numerical [1, 2, 3, 4]\n", "\n", "    print(f\"[Look-Up][Article {title}] {word_count} words\")\n", "\n", "    model_choices = []\n", "    lookup_page_ids = []\n", "\n", "    shortened_pages_pidx = []\n", "    for i, shortened_text in enumerate(shortened_pages):\n", "        shortened_pages_pidx.append(\"<Page {}>\\n\".format(i) + shortened_text)\n", "    shortened_article = '\\n'.join(shortened_pages_pidx)\n", "\n", "    expanded_gist_word_counts = []\n", "    for i, label in enumerate(gold_labels):\n", "        # only test the first question for demo\n", "        if i != 1:\n", "            continue\n", "        q = questions[i]\n", "        print(\"question: \", q)\n", "        options_i = [f\"{ol} {o}\" for ol, o in zip(choices, options[i])]\n", "        print(\"options: \", \"\\n\".join(options_i))\n", "        prompt_lookup = prompt_lookup_template.format(\n", "            shortened_article, q, '\\n'.join(options_i))\n", "\n", "        page_ids = []\n", "\n", "        response = query_model(prompt=prompt_lookup).strip()\n", "\n", "        try:\n", "            start = response.index('[')\n", "        except ValueError:\n", "            start = len(response)\n", "        try:\n", "            end = response.index(']')\n", "        except ValueError:\n", "            end = 0\n", "        if start < end:\n", "            page_ids_str = response[start+1:end].split(',')\n", "            page_ids = []\n", "            for p in page_ids_str:\n", "                if p.strip().isnumeric():\n", "                    page_id = int(p)\n", "                    if page_id < 0 or page_id >= len(pages):\n", "                        print(\"Skip invalid page number: \", page_id, flush=True)\n", "                    else:\n", "                        page_ids.append(page_id)\n", "\n", "        if verbose:\n", "            print(\"Model chose to look up page {}\".format(page_ids))\n", "\n", "        # Memory expansion after look-up, replacing the target shortened page with the original page\n", "        expanded_shortened_pages = shortened_pages[:]\n", "        if len(page_ids) > 0:\n", "            for page_id in page_ids:\n", "                expanded_shortened_pages[page_id] = '\\n'.join(pages[page_id])\n", "\n", "        expanded_shortened_article = '\\n'.join(expanded_shortened_pages)\n", "        expanded_gist_word_count = count_words(expanded_shortened_article)\n", "        if verbose:\n", "            print(\"Expanded shortened article:\\n\",\n", "                  expanded_shortened_article, flush=True)\n", "        prompt_answer = prompt_answer_template.format(\n", "            expanded_shortened_article, q, '\\n'.join(options_i))\n", "\n", "        # 修改响应解析逻辑，添加默认值处理\n", "        model_choice = None\n", "        response = query_model(prompt=prompt_answer)\n", "        response = response.strip()\n", "\n", "        # 更健壮的选项匹配逻辑\n", "        for j, choice in enumerate(choices):\n", "            if response.startswith(f\"Answer: {choice}\") or \\\n", "               response.startswith(f\"Answer: {choice[1]}\") or \\\n", "               f\"({choice[1]})\" in response:  # 添加更多可能的匹配模式\n", "                model_choice = j+1\n", "                break\n", "\n", "        # 添加默认值处理\n", "        if model_choice is None:\n", "            print(\n", "                f\"Warning: Could not parse model choice from response: {response}\")\n", "            model_choice = 1  # 默认选择第一个选项\n", "\n", "        # 确保label和model_choice都有有效值\n", "        label = gold_labels[i] if i < len(gold_labels) else 1\n", "        is_correct = 1 if model_choice == label else 0\n", "\n", "        print(f\"question: {q}\")\n", "        print(\n", "            f\"reference answer: {choices[label-1]}, model prediction: {choices[model_choice-1]}, is_correct: {is_correct}\")\n", "        print(\n", "            f\"compression rate {round(100.0 - gist_word_count/word_count*100, 2)}% ({gist_word_count}/{word_count})\")\n", "        print(\n", "            f\"compression rate after look-up {round(100.0 - expanded_gist_word_count/word_count*100, 2)}% ({expanded_gist_word_count}/{word_count})\")\n", "\n", "\n", "quality_parallel_lookup(example_with_gists)"]}, {"cell_type": "markdown", "metadata": {"id": "Gn8fjomx7iRz"}, "source": ["#Prompts that we used in the paper\n", "\n", "In the following we show the prompts that were used for the QuALTIY, QMSum, NarrativeQA datasets with the PaLM 2-L model. While there are slight differences in prompt design, most of these are not due to optimizing prompts for specific datasets but rather a results of that each author wrote the prompts independently."]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "PGvYRIpO3J3Y"}, "outputs": [], "source": ["# @title The prompts we used for QuALITY with PaLM 2-L\n", "\n", "\n", "# Pagination\n", "pagination_prompt_template = \"\"\"\n", "You are given a passage that is taken from a larger text (article, book, ...) and some numbered labels between the paragraphs in the passage.\n", "Numbered label are in angeled brackets. For example, if the label number is 19, it shows as <19> in text.\n", "Please choose one label that it is natural to break reading.\n", "Such point can be scene transition, end of a dialogue, end of an argument, narrative transition, etc.\n", "Please answer the break point label and explain.\n", "For example, if <57> is a good point to break, answer with \\\"Break point: <57>\\n Because ...\\\"\n", "\n", "Passage:\n", "\n", "{passage_text}\n", "{end_tag}\n", "\n", "\"\"\"\n", "# passage_text: a chunk of text.\n", "# end_tag: a string, whose value is \"\" if the text is at the end of the article, and otherwise \"\\n...\".\n", "\n", "\n", "# Gisting\n", "gisting_prompt_template = \"\"\"\n", "Please shorten the following passage.\n", "Just give me a shortened version. DO NOT explain your reason.\n", "\n", "Passage:\n", "{page_text}\n", "\n", "\"\"\"\n", "# page_text: a page of text\n", "\n", "\n", "# Parallel Look-up (ReadAgent-P, up to 5 pages)\n", "parallel_lookup_prompt_template = \"\"\"\n", "The following text is what you remembered from reading an article and a multiple choice question related to it.\n", "You may read 1 to 5 page(s) of the article again to refresh your memory to prepare yourselve for the question.\n", "Please respond with which page(s) you would like to read again.\n", "For example, if your would like to only read Page 8, respond with \\\"I want to look up Page [8] to ...\\\";\n", "if your would like to read Page 7 and 12, respond with \\\"I want to look up Page [7, 12] to ...\\\";\n", "if your would like to read Page 2, 3, 7, 15 and 18, respond with \\\"I want to look up <PERSON> [2, 3, 7, 15, 18] to ...\\\".\n", "DO NOT select more pages if you don't need to.\n", "DO NOT answer the question yet.\n", "\n", "Text:\n", "{concatenated_gists}\n", "\n", "Question:\n", "{question}\n", "{options}\n", "\n", "Take a deep breath and tell me: Which page(s) would you like to read again?\n", "\"\"\"\n", "# concatenated_gists: concatenated gists\n", "# question: a question\n", "# options: multiple-choice options\n", "\n", "\n", "# Sequential Look-up (ReadAgent-S, up to 5 pages)\n", "sequential_lookup_prompt_template = \"\"\"\n", "The following text is what you remember from reading an article, followed by a question about the article.\n", "You may read multiple pages of the article again to refresh your memory and prepare to answer the question.\n", "Each page that you re-read can significantly improve your chance of answering the question correctly.\n", "Please specify a SINGLE page you would like to read again or say \"STOP\".\n", "To read a page again, respond with \"Page $PAGE_NUM\", replacing $PAGE_NUM with the target page number.\n", "You can only specify a SINGLE page in your response at this time.\n", "DO NOT select more pages if you don't need to.\n", "To stop, simply say \"STOP\".\n", "DO NOT answer the question in your response.\n", "\n", "Text:\n", "{concatenated_gists}\n", "End of text.\n", "\n", "Pages re-read already (DO NOT ask to read them again):\n", "{past_page_numbers}\n", "\n", "Question:\n", "{question}\n", "{options}\n", "\n", "Specify a SINGLE page to read again, or say STOP:\n", "\"\"\"\n", "# concatenated_gists: concatenated gists\n", "# past_page_numbers: page numbers that have already been retrieved\n", "# question: a question\n", "# options: options\n", "\n", "\n", "# Response/Answer\n", "answer_prompt_template = \"\"\"\n", "Read the following article and answer a multiple choice question.\n", "For example, if (C) is correct, answer with \\\"Answer: (C) ...\\\"\n", "\n", "Article:\n", "{concatenated_pages_and_gists}\n", "\n", "Question:\n", "{question}\n", "{options}\n", "\n", "\"\"\"\n", "# concatenated_pages_and_gists: concatenated raw pages and gists\n", "# question: a question\n", "# options: options"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "Ya48p13EhhhI"}, "outputs": [], "source": ["# @title The prompts we used for QMSum with PaLM 2-L\n", "\n", "\n", "# Pagination\n", "pagination_prompt_template = \"\"\"\n", "You are given a passage that is taken from a larger meeting transcript.\n", "There are some numbered labels between the paragraphs (like <0>) in the passage.\n", "Please choose one label at a natural transition in the passage.\n", "For example, the label can be at the end of a dialogue, the end of an argument, a change in the topic being discussed, etc.\n", "Please respond with the label and explain your choice.\n", "For example, if <57> is a natural transition, answer with \"Label: <57>\\n Because ...\"\n", "\n", "Passage:\n", "\n", "{preceding_text}\n", "{passage_text}\n", "{end_tag}\n", "\n", "\"\"\"\n", "# preceding_text: a fraction of previous context\n", "# passage_text: a chunk of text.\n", "# end_tag: a string, whose value is \"\" if the text is at the end of the article, and otherwise \"\\n...\".\n", "\n", "\n", "# Gisting\n", "gisting_prompt_template = \"\"\"\n", "Please shorten the following passage.\n", "Just give a shortened version. DO NOT explain your reasoning.\n", "\n", "Passage:\n", "{page_text}\n", "\n", "\"\"\"\n", "# page_text: a page of text\n", "\n", "\n", "# Parallel Look-up (ReadAgent-P, up to 2 pages)\n", "parallel_lookup_prompt_template = \"\"\"\n", "The following text is what you remember from reading a meeting transcript, followed by a question about the transcript.\n", "You may read 1 or 2 pages of the transcript again to refresh your memory to prepare to answer the question.\n", "Please respond with which page(s) you would like to read.\n", "For example, if your would only like to read Page 8, respond with \"I want to look up Page [8] ...\"\n", "If you would like to read Page 7 and 12, respond with \"I want to look up <PERSON> [7, 12] ...\".\n", "Only select as many pages as you need, but no more than 2 pages.\n", "Don't answer the question yet.\n", "\n", "Text:\n", "{text}\n", "End of text.\n", "\n", "Question:\n", "{question}\n", "\n", "Which page(s) would you like to look up?\n", "\"\"\"\n", "# concatenated_gists: Concatenated gists\n", "# question: a question\n", "\n", "\n", "# Sequential Look-up (ReadAgent-S)\n", "sequential_lookup_prompt_template = \"\"\"\n", "The following text is what you remember from reading a meeting transcript, followed by a question about the transcript.\n", "You may read multiple pages of the transcript again to refresh your memory and prepare to answer the question.\n", "Each page that you re-read can significantly improve your chance of answering the question correctly.\n", "Please specify a SINGLE page you would like to read again or say \"STOP\".\n", "To read a page again, respond with \"Page $PAGE_NUM\", replacing $PAGE_NUM with the target page number.\n", "You can only specify a SINGLE page in your response at this time.\n", "DO NOT select more pages if you don't need to.\n", "To stop, simply say \"STOP\".\n", "DO NOT answer the question in your response.\n", "\n", "Text:\n", "{concatenated_gists}\n", "End of text.\n", "\n", "Pages re-read already (DO NOT ask to read them again):\n", "{past_page_numbers}\n", "\n", "Question:\n", "{question}\n", "\n", "Specify a SINGLE page to read again, or say STOP:\n", "\"\"\"\n", "# concatenated_gists: concatenated gists\n", "# past_page_numbers: page numbers that have already been retrieved\n", "# question: a question\n", "\n", "\n", "# Response/Answer\n", "answer_prompt_template = \"\"\"\n", "Read the question and text below and then answer the question.\n", "\n", "Question:\n", "{question}\n", "\n", "Text:\n", "{concatenated_pages_and_gists}\n", "End of Text.\n", "\n", "Answer the question based on the above passage and retrieved pages. Your answer should be short and concise.\n", "\"\"\"\n", "# question: a question\n", "# concatenated_pages_and_gists: concatenated raw pages and gists"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "U210HZJuP4_u"}, "outputs": [], "source": ["# @title The prompts we used for NarrativeQA - Gutenburg with PaLM 2-L\n", "\n", "\n", "# Pagination\n", "pagination_prompt_template = \"\"\"\n", "You are given a passage that is taken from a larger text (article, book, ...) and some numbered labels between the paragraphs in the passage.\n", "Numbered label are in angeled brackets. For example, if the label number is 19, it shows as <19> in text.\n", "Please choose one label that marks a major section break point.\n", "Such points can be the beginning/end of a book, beginning/end of a chapter, end of a content table, a scene transition, end of a dialogue, etc.\n", "If a point is chosen for the beginning of a book/chapter/etc and there is a title of the new book/chapter/etc, the break point must be chosen at a position right before the section number and title, not after.\n", "\n", "Please answer the break point label and explain.\n", "For example, if <57> is a good point to break, answer with \\\"Breakpoint: <57> ...\\\"\n", "\n", "Text:\n", "\n", "{preceding_text}\n", "{passage_text}\n", "{end_tag}\n", "\n", "\"\"\"\n", "# preceding_text: a fraction of previous context\n", "# passage_text: a chunk of text.\n", "# end_tag: a string, whose value is \"\" if the text is at the end of the article, and otherwise \"\\n...\".\n", "\n", "\n", "# Gisting\n", "gisting_prompt_template = \"\"\"\n", "Please shorten the following passage.\n", "Just give me a shortened version. DO NOT explain your reason.\n", "\n", "Passage:\n", "{page_text}\n", "\n", "\"\"\"\n", "# page_text: a page of text\n", "\n", "\n", "# Parallel Look-up (ReadAgent-P, up to 2 pages)\n", "parallel_lookup_prompt_template = \"\"\"\n", "The following text is what you remembered from reading an article and a question related to it.\n", "You may read 1 or 2 page(s) of the article again to refresh your memory to prepare yourselve for the question.\n", "Please respond with which page(s) you would like to read in the order of importance, beginning with the most important page number.\n", "For example, if your only need to read Page 8, respond with \\\"I want to look up Page [8] to ...\\\";\n", "if your would like to read Page 12 and 7, respond with \\\"I want to look up Page [12, 7] to ...\\\";\n", "DO NOT select more pages if you don't need to.\n", "You don't need to answer the question yet.\n", "\n", "Text:\n", "{concatenated_gists}\n", "\n", "Question:\n", "{question}\n", "\n", "\"\"\"\n", "# concatenated_gists: Concatenated gists\n", "# question: a question\n", "\n", "\n", "# Sequential Look-up (ReadAgent-S)\n", "sequential_lookup_prompt_template = \"\"\"\n", "The following text is what you remember from reading a meeting transcript, followed by a question about the transcript.\n", "You may read multiple pages of the transcript again to refresh your memory and prepare to answer the question.\n", "Each page that you re-read can significantly improve your chance of answering the question correctly.\n", "Please specify a SINGLE page you would like to read again or say \"STOP\".\n", "To read a page again, respond with \"Page $PAGE_NUM\", replacing $PAGE_NUM with the target page number.\n", "You can only specify a SINGLE page in your response at this time.\n", "DO NOT select more pages if you don't need to.\n", "To stop, simply say \"STOP\".\n", "DO NOT answer the question in your response.\n", "\n", "Text:\n", "{concatenated_gists}\n", "End of text.\n", "\n", "Pages re-read already (DO NOT ask to read them again):\n", "{past_page_numbers}\n", "\n", "Question:\n", "{question}\n", "\n", "Specify a SINGLE page to read again, or say STOP:\n", "\"\"\"\n", "# concatenated_gists: concatenated gists\n", "# past_page_numbers: page numbers that have already been retrieved\n", "# question: a question\n", "\n", "\n", "# Response/Answer\n", "answer_prompt_template = \"\"\"\n", "{concatenated_pages_and_gists}\n", "\n", "Question:\n", "{question}\n", "\n", "Answer the question based on the above passage and retrieved pages. Your answer should be short and concise.\n", "\"\"\"\n", "# concatenated_pages_and_gists: concatenated raw pages and gists\n", "# question: a question"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "K1pw1NPasHPC"}, "outputs": [], "source": ["# @title The prompts we used for NarrativeQA - Movie Scripts with PaLM 2-L\n", "\n", "\n", "# Pagination\n", "pagination_prompt_template = \"\"\"\n", "You are given a movie script and some numbered labels between the lines in the script.\n", "Numbered label are in angeled brackets.\n", "Please choose one label that it is natural to break reading. The label should be between <{start}> and <{end}>.\n", "Such point can be scene transition, end of a dialogue, end of an argument, narrative transition, etc.\n", "The answer should end with \"The break point is: <number>\", where the break point number is between angeled brackets.\n", "\n", "Script:\n", "\n", "{passage_text}\n", "\"\"\"\n", "# passage_text: a chunk of text.\n", "\n", "\n", "# Gisting\n", "gisting_prompt_template = \"\"\"\n", "Please shorten the following passage. The shortened passage should be in 128 tokens. Please refer to people with their full names whenever possible.\n", "Just give me a shortened version. DO NOT explain your reason. If there is no meaning information in the passage, output \"I don't have enough information to shorten the passage.\"\n", "\n", "Passage:\n", "{page_text}\n", "\n", "\"\"\"\n", "# page_text: a page of text\n", "\n", "\n", "# Parallel Look-up (ReadAgent-P, up to 2 pages)\n", "parallel_lookup_prompt_template = \"\"\"\n", "The following text includes a summary of each page in a movie script, followed by a question about the script.\n", "\n", "Summary:\n", "{concatenated_gists}\n", "\n", "Question:\n", "{question}\n", "\n", "Based on the summary of each page, you may read the full details of 1 to 2 page(s) to obtain more information to answer the question.\n", "Please respond with which page(s) you would like to read.\n", "For example, if you only need to read Page X_0, the answer should end with \\\"I want to look up Page [X_0]\\\";\n", "if you would like to read Page X_0 and X_1, the answer should end with \\\"I want to look up Page [X_0, X_1]\\\".\n", "X_i above is a page index between 0 and {end}. DO NOT select more pages if you don't need to.\n", "You don't need to answer the question yet.\n", "\"\"\"\n", "# concatenated_gists: Concatenated gists\n", "# question: a question\n", "\n", "\n", "# Sequential Look-up (ReadAgent-S)\n", "sequential_lookup_prompt_template = \"\"\"\n", "The following text includes a summary of each page in a movie script, followed by a question about the script, and the previous answer based on the summary and already re-read pages.\n", "\n", "Summary:\n", "{concatenated_gists}\n", "\n", "Pages re-read already (DO NOT ask to read them again):\n", "{past_page_numbers}\n", "\n", "Question:\n", "{question}\n", "\n", "Previous Answer:\n", "{previous_answer}\n", "\n", "Based on the summary of each page, you may read the full details of multiple pages to obtain more information to answer the question.\n", "To read a page again, respond with \"I want to look up Page $PAGE_NUM\", replacing $PAGE_NUM with the target page number.\n", "PAGE_NUM is a page index between 0 and {end}, excluding {pages_reread}.\n", "You can only specify a SINGLE page in your response at this time. The page should not be in the re-read pages.\n", "To stop, simply say \"STOP\".\n", "DO NOT answer the question in your response.\n", "You don't need to answer the question yet.\n", "\"\"\"\n", "# concatenated_gists: concatenated gists\n", "# past_page_numbers: (only after the first query) page numbers that have already been retrieved\n", "# question: a question\n", "# previous_answer: the previous answer given by the model with gists and previously retrieved raw pages\n", "\n", "\n", "# Response/Answer\n", "answer_prompt_template = \"\"\"\n", "{concatenated_pages_and_gists}\n", "\n", "Question:\n", "{question}\n", "\n", "Answer the question based on the above passage and retrieved pages. Your answer should be short and concise.\n", "\"\"\"\n", "# concatenated_pages_and_gists: concatenated raw pages and gists\n", "# question: a question"]}], "metadata": {"colab": {"provenance": [], "toc_visible": true}, "kernelspec": {"display_name": "Python 3", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "undefined.undefined.undefined"}}, "nbformat": 4, "nbformat_minor": 0}