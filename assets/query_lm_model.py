import requests
import time
import datetime

API_URL = 'http://192.168.3.70:1234'
API_KEY = 'lm_studio'
MODEL = 'qwen3-30b-a3b'

def query_lm_model(
    prompt: str,
    enable_thinking: bool = False,
    temperature: float = 0.0,
    max_decode_steps: int = 512,
    seconds_to_reset_tokens: float = 30.0,
) -> str:
    headers = {
        'Authorization': f'Bearer {API_KEY}',
        'Content-Type': 'application/json'
    }
    
    payload = {
        'model': MODEL,
        'prompt': prompt,
        'temperature': temperature,
        'max_tokens': max_decode_steps,
        'enable_thinking': enable_thinking
    }

    while True:
        try:
            response = requests.post(
                API_URL,
                headers=headers,
                json=payload,
                timeout=30
            )
            response.raise_for_status()
            
            # Parse response - adjust based on actual API response structure
            data = response.json()
            return data.get('choices', [{}])[0].get('message', {}).get('content', '')
            
        except requests.exceptions.RequestException as e:
            if isinstance(e, requests.exceptions.HTTPError) and e.response.status_code == 429:
                print(f'{datetime.datetime.now()}: query_lm_model: RateLimitError: {e}')
                time.sleep(seconds_to_reset_tokens)
            else:
                print(f'{datetime.datetime.now()}: query_lm_model: APIError: {e}')
                print(f'{datetime.datetime.now()}: query_lm_model: Retrying after 5 seconds...')
                time.sleep(5)
