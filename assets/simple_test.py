#!/usr/bin/env python3
"""
简化的Read Agent测试 - 《竞选州长》
"""

import read_agent
import time

def load_story():
    """加载《竞选州长》文本"""
    with open('running_for_governer.txt', 'r', encoding='utf-8') as f:
        full_text = f.read()
    
    # 提取《竞选州长》部分
    start_marker = "RUNNING FOR GOVERNOR--[Written about 1870.]"
    end_marker = "MARK TWAIN, LP., M.T., B.S., D.T., F.C., and L.E."
    
    start_idx = full_text.find(start_marker)
    end_idx = full_text.find(end_marker) + len(end_marker)
    
    story_text = full_text[start_idx:end_idx].strip()
    return story_text

def main():
    print("Read Agent 简化测试 - 《竞选州长》")
    print("=" * 50)
    
    # 加载文本
    text = load_story()
    stats = read_agent.get_text_statistics(text)
    print(f"文本统计: {stats['total_words']} 词, {stats['paragraphs']} 段落")
    
    # 单个测试查询
    query = "What accusations were made against <PERSON> during his campaign?"
    print(f"\n查询: {query}")
    
    try:
        start_time = time.time()
        
        result = read_agent.process_long_text(
            text=text,
            query=query,
            verbose=True,
            save_path="simple_test"
        )
        
        end_time = time.time()
        
        print(f"\n✓ 处理完成，耗时: {end_time - start_time:.2f}秒")
        print(f"✓ 压缩率: {result['compression_rate']:.2%}")
        print(f"✓ 使用页面: {result['relevant_pages']}")
        print(f"✓ 总页面数: {len(result['pages'])}")
        
        print(f"\n回答:")
        print("-" * 50)
        print(result['response'])
        print("-" * 50)
        
    except Exception as e:
        print(f"✗ 测试失败: {e}")

if __name__ == "__main__":
    main()
