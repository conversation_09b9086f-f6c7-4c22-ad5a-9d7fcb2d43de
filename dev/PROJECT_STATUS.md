# 项目状态与未来规划文档

**文档目的**: 本文档旨在为新接手本项目的AI Agent或人类程序员提供全面的背景信息，包括项目至今的成果、代码结构、核心功能、当前遇到的挑战以及经过深入讨论后确定的未来架构方案。

---

## 1. 项目概述

### 1.1. 核心目标
本项目旨在开发一个AI驱动的智能阅读系统。系统的核心功能是处理用户上传的EPUB格式电子书，通过LLM技术对内容进行深度理解和语义分块，并能通过与用户的自然语言对话，精准回答基于书籍内容的问题。一个关键的用户体验要求是，系统在回答问题时，必须能提供返回到书中原文具体位置的导航链接（CFI）。

### 1.2. 当前状态
项目目前处于一个关键的 **“架构重构与技术验证”** 阶段。

初版的端到端数据处理流程已成功实现并验证（包括UUID数据库迁移、多LLM供应商支持等），但在核心的 **CFI (Canonical Fragment Identifier) 生成** 功能上遇到了重大且难以逾越的障碍。

经过多次尝试和深入讨论，我们已经确定了一个全新的、更健壮的架构方案，后续的开发工作将围绕这个新架构展开。

---

## 2. 已完成的成果

- **UUID数据库迁移**: 项目的数据库主键已从传统的自增整数，成功迁移为UUID（字符串格式）。这解决了在未来合并多个书籍数据库时可能出现的主键冲突问题，为系统的扩展性打下了良好基础。
- **插件化处理架构**: 系统设计了基于钩子（Hooks）的插件化架构（详见 `hooks/` 和 `system_plugins/`）。这允许开发者轻松地扩展对文本块的处理功能，例如添加新的摘要算法、关键词提取、向量嵌入等，而无需修改核心处理流程。
- **多LLM供应商支持**: `llm/llm_interface.py` 提供了一个统一的接口，可以方便地切换和管理不同的LLM供应商（如 `deepseek`, `kimi`），具有良好的扩展性。
- **初步的端到端流程**: 一个完整的（尽管CFI部分有缺陷）处理流程已经打通：`main.py` -> `epub_parser.py` -> `core_processor.py` -> `storage_manager.py`。

---

## 3. 代码结构与核心模块说明

- **`main.py`**: **[待重构]** 项目的入口。目前是一个简单的命令行脚本，用于触发对单个EPUB文件的处理。在未来的新架构中，它将被重构为一个基于 **FastAPI** 的Web服务器。

- **`config.py`**: 配置文件，主要用于设置默认的LLM provider。

- **`storage/storage_manager.py`**: 数据库管理器。
  - **功能**: 负责所有与SQLite数据库的交互，包括创建表、增删改查。
  - **Schema**: 包含 `books`, `chapters`, `pages`, `processed_data` 等表。**[待重构]** `pages` 表的CFI存储方式需要调整，以支持存储CFI列表。

- **`book_parsers/epub_parser.py`**: **[待重构 - 核心重构模块]**
  - **原功能**: 负责解析EPUB文件，提取文本内容，并尝试生成CFI。
  - **现状**: **此模块是当前所有问题的核心**，其中CFI的生成逻辑经多次验证均不符合EPUB 3标准。**此文件将被完全重写**。

- **`processing/core_processor.py`**: 核心处理器。
  - **功能**: 编排数据处理流水线，将解析出的内容进行分块、分页，并调用插件进行处理。
  - **`paginate_text` 函数**: **[待重构]** 这是之前用LLM寻找语义断点的分页函数。在新架构中，它的功能和角色将被保留，但其输入将变为句子列表，而不是整章文本。

- **`llm/llm_interface.py`**: LLM接口，用于与大语言模型API交互。

- **`hooks/` & `plugin_manager.py`**: 插件系统定义。

- **`system_plugins/`**: 系统默认插件的实现，例如 `gisting` (摘要)。

---

## 4. 当前的挑战：CFI生成

### 4.1. 问题描述
在开发过程中，我们发现使用纯Python库为EPUB 3文件生成完全兼容的、精确的CFI极其困难。所有尝试生成的CFI都与标准阅读器（如Calibre）生成的CFI在结构上存在根本性差异，导致无法正确定位。

### 4.2. 失败的尝试（重要：避免重蹈覆辙）
1.  **自行实现的简易算法**: 最初的实现过于简单，无法处理复杂的EPUB结构。
2.  **`epubcfi` 库**: 经过深入研究，发现此库是一个CFI **解析器** 和 **处理器**，不具备从文档元素生成CFI的功能。
3.  **`pycfi` 库**: 该库虽然能生成CFI，但其算法不符合标准，生成的路径与Calibre不兼容。
4.  **翻译 `epub-cfi-generator` (Node.js)**: 尝试将一个可行的Node.js库翻译为Python。但由于Python的`lxml`和JavaScript的`xmldom`在解析DOM树时存在无法远程调试的细微差异，导致翻译后的CFI路径依然不正确。

### 4.3. 根本原因
Python生态系统目前似乎缺少一个能够开箱即用、稳定可靠地生成EPUB 3标准CFI的库。这项任务的复杂性远超预期。

---

## 5. 最终的架构方案与下一步

为了从根本上解决CFI的生成难题，同时保留我们现有代码的优势，我们确定了以下“混合模式”的新架构：

### 5.1. 核心思想
将CFI的生成工作交给最专业的工具（前端的`epub.js`），将繁重的预处理工作保留在后端，同时复用后端高质量的中文分句逻辑和LLM语义分割能力。

### 5.2. 新工作流程
1.  **后端触发**: 后端接收到用户上传的EPUB文件，启动一个异步后台任务。
2.  **后端调用Playwright**: 后台任务启动一个 **Playwright无头浏览器**，在其中加载 `epub.js`。
3.  **`epub.js`生成段落CFI**: 在浏览器环境中，`epub.js` 的任务是：遍历书中所有段落（`<p>`），为 **每个段落** 生成 `{ "paragraph_text": "...", "paragraph_cfi": "..." }`。它不负责分句。
4.  **数据返回Python**: `epub.js` 将上述 **段落级别** 的JSON数组返回给Python主进程。
5.  **Python分句**: Python进程拿到段落文本后，调用我们现有的 `text_utils.parse_text` 函数进行高质量的分句。
6.  **Python生成句子CFI**: Python根据每个句子在段落文本中的字符偏移量，通过字符串拼接，将段落CFI扩展为每个句子的精确范围CFI (e.g., `f"{paragraph_cfi}/1:{start_offset}"`)。
7.  **LLM处理与存储**: 后端拿到这份“句子-CFI”精确对应列表后，再执行现有的LLM语义分页逻辑，形成最终的、携带CFI列表的语义文本块，并存入数据库。

### 5.3. 下一步行动计划
1.  **添加新依赖**: 为项目添加 `fastapi`, `uvicorn`, `playwright` 等Python库。
2.  **重构 `main.py`**: 将其改造为一个FastAPI应用，提供文件上传接口，并能异步触发后台处理任务。
3.  **重写 `epub_parser.py`**: 实现上述第2、3、4、5、6步的逻辑，即通过Playwright调用`epub.js`并处理返回数据的完整流程。
4.  **重构 `core_processor.py`**: 调整其入口函数，使其能接收“句子-CFI”列表，并执行LLM语义分页。
5.  **调整 `storage_manager.py`**: 修改数据库表结构，以支持存储一个文本块对应的 **CFI列表**（可以存为JSON字符串）。
