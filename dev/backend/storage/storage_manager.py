# dev/storage/storage_manager.py
import sqlite3
import json
import os
import uuid  # 导入uuid库
from ..config import DATA_DIR, SQLITE_DB_NAME

# 译者注：此处暂时不导入 chromadb，因为我们首先要实现SQLite部分
# import chromadb


class StorageManager:
    def __init__(self):
        """初始化存储管理器，连接到数据库并设置集合。"""
        # 确保数据目录存在
        os.makedirs(DATA_DIR, exist_ok=True)

        db_path = os.path.join(DATA_DIR, SQLITE_DB_NAME)
        self.conn = sqlite3.connect(db_path)
        self.cursor = self.conn.cursor()
        self.setup_databases()

        # ChromaDB 初始化 (暂时注释掉)
        # self.chroma_client = chromadb.PersistentClient(path=CHROMA_PATH)
        # self.collection = self.chroma_client.get_or_create_collection(
        #     name=CHROMA_COLLECTION_NAME
        # )

    def setup_databases(self):
        """创建所有必要的数据库表（如果它们不存在）。"""
        # 创建 books 表, id 使用 TEXT 作为 UUID 主键
        self.cursor.execute("""
        CREATE TABLE IF NOT EXISTS books (
            id TEXT PRIMARY KEY,
            source_path TEXT NOT NULL UNIQUE,
            content_hash TEXT NOT NULL,
            detected_language TEXT,
            metadata TEXT, -- JSON a
            status TEXT NOT NULL DEFAULT 'new',
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
        """)

        # 创建 chapters 表, id 和 book_id 使用 TEXT
        self.cursor.execute("""
        CREATE TABLE IF NOT EXISTS chapters (
            id TEXT PRIMARY KEY,
            book_id TEXT NOT NULL,
            chapter_index INTEGER NOT NULL,
            title TEXT,
            content TEXT,
            cfi_base TEXT, -- 指向该章节的基础CFI
            FOREIGN KEY (book_id) REFERENCES books (id)
        )
        """)

        # 创建 sentences 表, id 和 chapter_id 使用 TEXT
        self.cursor.execute("""
        CREATE TABLE IF NOT EXISTS sentences (
            id TEXT PRIMARY KEY,
            chapter_id TEXT NOT NULL,
            sentence_index_in_chapter INTEGER NOT NULL,
            content TEXT NOT NULL,
            start_cfi TEXT,
            end_cfi TEXT,
            FOREIGN KEY (chapter_id) REFERENCES chapters (id)
        )
        """)

        # 创建 pages 表, id 和 chapter_id 使用 TEXT
        self.cursor.execute("""
        CREATE TABLE IF NOT EXISTS pages (
            id TEXT PRIMARY KEY,
            chapter_id TEXT NOT NULL,
            page_index_in_chapter INTEGER NOT NULL,
            content TEXT,
            start_cfi TEXT, -- 页面的起始CFI
            end_cfi TEXT,   -- 页面的结束CFI
            FOREIGN KEY (chapter_id) REFERENCES chapters (id)
        )
        """)

        # 创建 processed_data 表, id 和 page_id 使用 TEXT
        self.cursor.execute("""
        CREATE TABLE IF NOT EXISTS processed_data (
            id TEXT PRIMARY KEY,
            page_id TEXT NOT NULL,
            processor_name TEXT NOT NULL, -- 例如: 'gisting', 'keyword_extraction'
            data TEXT, -- JSON 格式的处理结果
            vector_id TEXT, -- 存储在ChromaDB中的ID
            FOREIGN KEY (page_id) REFERENCES pages (id)
        )
        """)

        self.conn.commit()

    def add_book(self, source_path: str, content_hash: str, detected_language: str, metadata: dict) -> str:
        """添加一个新书籍记录，如果不存在的话。返回书籍的UUID。"""
        self.cursor.execute(
            "SELECT id FROM books WHERE source_path = ?", (source_path,))
        row = self.cursor.fetchone()
        if row:
            return row[0]
        else:
            book_uuid = str(uuid.uuid4())
            self.cursor.execute(
                "INSERT INTO books (id, source_path, content_hash, detected_language, metadata) VALUES (?, ?, ?, ?, ?)",
                (book_uuid, source_path, content_hash, detected_language,
                 json.dumps(metadata, ensure_ascii=False))
            )
            self.conn.commit()
            return book_uuid

    def add_chapter_only(self, book_id: str, chapter_index: int, title: str, content: str, cfi_base: str) -> str:
        """添加单个章节的内容，返回章节的UUID。"""
        chapter_uuid = str(uuid.uuid4())
        self.cursor.execute(
            "INSERT INTO chapters (id, book_id, chapter_index, title, content, cfi_base) VALUES (?, ?, ?, ?, ?, ?)",
            (chapter_uuid, book_id, chapter_index, title, content, cfi_base)
        )
        self.conn.commit()
        return chapter_uuid

    def add_sentence_only(self, chapter_id: str, sentence_index_in_chapter: int, content: str, start_cfi: str, end_cfi: str) -> str:
        """添加单个句子的内容，返回句子的UUID。"""
        sentence_uuid = str(uuid.uuid4())
        self.cursor.execute(
            "INSERT INTO sentences (id, chapter_id, sentence_index_in_chapter, content, start_cfi, end_cfi) VALUES (?, ?, ?, ?, ?, ?)",
            (sentence_uuid, chapter_id, sentence_index_in_chapter, content, start_cfi, end_cfi)
        )
        self.conn.commit()
        return sentence_uuid

    def add_page_only(self, chapter_id: str, page_index_in_chapter: int, content: str, start_cfi: str, end_cfi: str) -> str:
        """添加单个页面的内容，返回页面的UUID。"""
        page_uuid = str(uuid.uuid4())
        self.cursor.execute(
            "INSERT INTO pages (id, chapter_id, page_index_in_chapter, content, start_cfi, end_cfi) VALUES (?, ?, ?, ?, ?, ?)",
            (page_uuid, chapter_id, page_index_in_chapter, content, start_cfi, end_cfi)
        )
        self.conn.commit()
        return page_uuid

    def add_processed_data(self, page_id: str, processor_name: str, data: str) -> str:
        """添加处理后的数据，返回该记录的UUID。"""
        processed_data_uuid = str(uuid.uuid4())
        self.cursor.execute(
            "INSERT INTO processed_data (id, page_id, processor_name, data) VALUES (?, ?, ?, ?)",
            (processed_data_uuid, page_id, processor_name, data)
        )
        self.conn.commit()
        return processed_data_uuid

    def get_page_content(self, page_id: str) -> str:
        """根据页面ID获取页面内容。"""
        self.cursor.execute(
            "SELECT content FROM pages WHERE id = ?", (page_id,))
        row = self.cursor.fetchone()
        if row:
            return row[0]
        return None

    def close(self):
        """关闭数据库连接。"""
        if self.conn:
            self.conn.close()