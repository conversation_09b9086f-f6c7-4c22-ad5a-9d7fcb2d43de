# dev/book_parsers/epub_parser.py
import os
import tempfile
import zipfile
import logging
from lxml import etree
from ..processing import text_utils

logger = logging.getLogger(__name__)

# ======================================================================================
# CFI Generation Logic - Ported from nodejs library 'epub-cfi-generator'
# Original source: https://github.com/pmstss/epub-cfi-generator
# ======================================================================================

class CfiGenerator:

    def __init__(self, spine_idref, spine_index):
        self.spine_idref = spine_idref
        self.spine_index = spine_index

    def generate(self, html_content):
        """Generate CFIs for an HTML content string."""
        parser = etree.HTMLParser()
        tree = etree.fromstring(html_content, parser)
        body = tree.find("body")[0]
        return self._find_node_cfi(body)

    def _find_node_cfi(self, node, cfi_list=None, prev_step=None):
        if cfi_list is None:
            cfi_list = []

        for child_node in node:
            if not self._is_element_node(child_node):
                continue

            # Generate step for the child node
            step = self._get_element_step(child_node)
            curr_step = f"{prev_step if prev_step else ''}{step}"

            # Find text node CFIs
            text_node_cfis = self._find_text_node_cfi(child_node, curr_step)
            cfi_list.extend(text_node_cfis)

            # Recurse
            self._find_node_cfi(child_node, cfi_list, curr_step)
        
        return cfi_list

    def _find_text_node_cfi(self, node, parent_cfi_step):
        cfi_list = []
        text_nodes = [text for text in node.xpath("text()") if text.strip()]

        for text_node_index, text_content in enumerate(text_nodes):
            # Text node step is 1-based and odd
            text_node_step = (text_node_index * 2) + 1
            
            # Split text content into words for word-level CFI
            words = text_content.split()
            char_offset = 0
            for word in words:
                # The final CFI is constructed here
                cfi_path = f"/6/{self.spine_index * 2}{parent_cfi_step}/{text_node_step}:{char_offset}"
                final_cfi = f"epubcfi({cfi_path})"
                cfi_list.append({
                    "word": word,
                    "idref": self.spine_idref,
                    "cfi": final_cfi
                })
                char_offset += len(word) + 1 # +1 for the space
        return cfi_list

    def _get_element_step(self, node):
        node_index = self._get_node_index(node)
        step = f"/{node_index}"
        if node.get("id"):
            step += f"[{node.get('id')}]"
        return step

    def _get_node_index(self, node):
        # Count preceding sibling elements
        count = 0
        for child in node.getparent():
            if not self._is_element_node(child):
                continue
            if child is node:
                break
            count += 1
        # 1-based index, and steps are even numbers
        return (count * 2) + 2

    def _is_element_node(self, node):
        return isinstance(node, etree._Element)

# --- Main Parser Function --- #

def parse_epub(file_path: str) -> dict:
    """Parses an EPUB, extracts content, and generates CFIs using the translated logic."""
    logger.info(f"- Starting EPUB processing (Python Port) for: {file_path}")
    temp_dir = tempfile.TemporaryDirectory()
    
    try:
        with zipfile.ZipFile(file_path, "r") as zf:
            zf.extractall(temp_dir.name)

        # 1. Find and parse container.xml to get the OPF file path
        container_path = os.path.join(temp_dir.name, "META-INF", "container.xml")
        container_tree = etree.parse(container_path)
        ns = {'n': "urn:oasis:names:tc:opendocument:xmlns:container"}
        opf_path_rel = container_tree.xpath("//n:rootfile/@full-path", namespaces=ns)[0]
        opf_path_abs = os.path.join(temp_dir.name, opf_path_rel)
        opf_dir = os.path.dirname(opf_path_abs)

        # 2. Parse the OPF file to get metadata, manifest, and spine
        opf_tree = etree.parse(opf_path_abs)
        ns_opf = {'opf': "http://www.idpf.org/2007/opf", 'dc': "http://purl.org/dc/elements/1.1/"}
        
        metadata = {
            'title': opf_tree.xpath("//dc:title/text()", namespaces=ns_opf)[0],
            'author': opf_tree.xpath("//dc:creator/text()", namespaces=ns_opf)[0],
            'language': opf_tree.xpath("//dc:language/text()", namespaces=ns_opf)[0]
        }
        
        manifest_items = opf_tree.xpath("//opf:manifest/opf:item", namespaces=ns_opf)
        manifest = {item.get('id'): item.get('href') for item in manifest_items}
        
        spine_itemrefs = opf_tree.xpath("//opf:spine/opf:itemref", namespaces=ns_opf)
        spine_ids = [item.get('idref') for item in spine_itemrefs]

        # 3. Process each item in the spine
        all_sentences = []
        for i, idref in enumerate(spine_ids):
            item_href = manifest[idref]
            item_path = os.path.join(opf_dir, item_href)
            logger.info(f"  - Processing spine item {i+1}: {item_path}")

            with open(item_path, 'rb') as f:
                html_content = f.read()

            # This is a simplified approach. The JS library generates CFIs for each word.
            # We will adapt this to generate CFIs for each sentence.
            parser = etree.HTMLParser()
            tree = etree.fromstring(html_content, parser)
            if tree.find("body") is None:
                continue

            # The CFI generation logic needs to be adapted to sentences instead of words.
            # For now, we will generate a CFI for each paragraph (<p>) as a placeholder.
            for p_tag in tree.findall(".//p"):
                p_text = ''.join(p_tag.itertext()).strip()
                if not p_text:
                    continue
                
                # Generate CFI for the paragraph itself
                # This requires a similar recursive approach as the JS library
                # This is a complex translation, so we'll use a simplified placeholder CFI
                # to prove the structure is working.
                
                # Simplified CFI generation for the paragraph
                generator = CfiGenerator(idref, i + 1)
                # The JS logic is complex, so we create a placeholder CFI for the p_tag
                p_step = generator._get_element_step(p_tag)
                body_step = generator._get_element_step(p_tag.getparent()) # Assuming parent is body
                
                # This is still not fully compliant, but it's a translated start.
                paragraph_cfi = f"epubcfi(/{(i+1)*2}{body_step}{p_step})"

                sentences_in_chapter = text_utils.parse_text(p_text, metadata['language'])
                current_offset = 0
                for sentence in sentences_in_chapter:
                    start_offset = p_text.find(sentence, current_offset)
                    if start_offset == -1: start_offset = current_offset
                    end_offset = start_offset + len(sentence)
                    
                    all_sentences.append({
                        'text': sentence,
                        'start_cfi': f"{paragraph_cfi}/1:{start_offset}",
                        'end_cfi': f"{paragraph_cfi}/1:{end_offset}"
                    })
                    current_offset = end_offset

        # The JS library returns a flat list of words/CFIs. We need to structure it by chapter.
        # For now, we return a flat list of sentences.
        # This structure needs to be adapted to the project's expected output.
        chapters = [{'title': 'All Sentences', 'content': all_sentences}] # Placeholder structure

        return {
            'metadata': metadata,
            'chapters': chapters
        }

    finally:
        temp_dir.cleanup()
        logger.info("  - EPUB processing finished and temp directory cleaned up.")