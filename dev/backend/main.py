# dev/main.py
import sys
import os
import logging

# 将dev目录添加到sys.path，以便导入模块
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

from dev.book_parsers import epub_parser
from dev.storage.storage_manager import StorageManager
from dev.processing import core_processor

# Configure logging
logging.basicConfig(
    level=logging.DEBUG, # Set to DEBUG to capture all messages
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("read_agent.log"), # Log to a file
        logging.StreamHandler() # Also log to console
    ]
)
logger = logging.getLogger(__name__)

def main():
    """主执行函数。"""
    # 1. 初始化
    storage = StorageManager()
    
    # 2. 定义要处理的文件
    epub_file = os.path.join(os.path.dirname(__file__), 'corpus', '老人与海.epub')
    
    logger.info(f"--- 开始处理: {epub_file} ---")
    
    # 3. 解析书籍
    logger.info("\n[Step 1/2] 解析EPUB书籍...")
    book_data = epub_parser.parse_epub(epub_file)
    book_data['source_path'] = epub_file
    logger.info("   - 解析完成。")

    # 4. 处理书籍并存入数据库
    logger.info("\n[Step 2/2] 处理书籍并存入数据库...")
    core_processor.process_book(book_data, storage)
    logger.info("   - 处理和存储完成。")
    
    # 5. 关闭数据库连接
    storage.close()
    
    logger.info(f"\n--- 所有步骤完成: {epub_file} ---")


if __name__ == "__main__":
    main()
