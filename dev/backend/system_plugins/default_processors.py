# dev/system_plugins/default_processors.py
import pluggy
from ..processing.pipeline_objects import Page
from ..llm.llm_interface import query_llm, clean_llm_response
from ..llm.prompt_manager import PromptManager
from ..config import LANGUAGE_MAPPING

hookimpl = pluggy.HookimplMarker("read_agent")

@hookimpl
def process_page(page: Page, detected_language: str):
    """
    一个页面处理器插件，为页面生成摘要 (gisting)。
    """
    # 为了解耦，插件在需要时自行初始化一个PromptManager。
    # 在一个更大型的应用中，这可以通过依赖注入来优化。
    prompt_manager = PromptManager()
    
    gisting_template = prompt_manager.get_template('gisting')
    if not gisting_template:
        # 如果没有摘要模板，则不执行任何操作
        return

    prompt = gisting_template.format(
        language=LANGUAGE_MAPPING.get(detected_language, 'English'),
        page_text=page.content
    )
    
    summary = clean_llm_response(query_llm(prompt))
    
    # 将处理结果存回传入的Page对象中
    if summary:
        page.processed_data['gisting'] = summary
