# dev/config.py

# 默认处理配置，区分中英文
DEFAULT_CONFIG = {
    'zh': {
        'unit_limit': 1000,         # 分页时每页的单位上限（混合单位）
        'start_threshold': 300,    # 开始寻找断点标记的单位阈值
        'max_lookup_pages': 5,      # 查找相关页面时返回的最大页面数
        'temperature': 0.0,         # LLM 生成时的温度参数，0.0 表示更确定的输出
        'unit_type': 'char_mixed'   # 中文的单位计数类型：混合模式
    },
    'en': {
        'unit_limit': 1000,         # 分页时每页的单位上限（单词数）
        'start_threshold': 300,    # 开始寻找断点标记的单位阈值
        'max_lookup_pages': 5,      # 查找相关页面时返回的最大页面数
        'temperature': 0.0,         # LLM 生成时的温度参数
        'unit_type': 'word'         # 英文的单位计数类型：单词
    }
}

# LLM API 配置
LLM_CONFIG = {
    "default_provider": "deepseek",
    "providers": {
        "ollama": {
            "api_url": "http://localhost:11434/api/chat",
            "model": "qwen3:8b",
            "max_tokens": 4096,
            "max_retries": 5
        },
        "deepseek": {
            "api_url": "https://api.deepseek.com/chat/completions",
            "api_key": "sk-4c4b628af3244744913b0bc7cc10fe42",
            "model": "deepseek-chat",
            "max_tokens": 4096,
            "max_retries": 3
        },
        "kimi": {
            "api_url": "https://api.moonshot.cn/v1/chat/completions",
            "api_key": "sk-YdaW23x7OIF3XcKjwIoTWoXpPh4E7Z0dp3wNK2e1FIqTwy6q",
            "model": "kimi-k2-0711-preview",
            "max_tokens": 4096,
            "max_retries": 3
        }
    }
}

# 数据库配置
DATA_DIR = "with_sqlite_chroma/dev/data"
SQLITE_DB_NAME = "books.db"
CHROMA_COLLECTION_NAME = "read_agent_v2"

# 调试配置
DEBUG_MODE = False

# 语言代码到提示中语言名称的映射
LANGUAGE_MAPPING = {
    'zh': 'Chinese (中文)',
    'en': 'English'
}
