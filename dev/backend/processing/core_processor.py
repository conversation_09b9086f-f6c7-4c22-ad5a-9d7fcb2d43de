# dev/processing/core_processor.py
from typing import List, Dict, Optional, Tuple
import re
from concurrent.futures import ThreadPoolExecutor, as_completed
import logging
import hashlib
import pluggy

from ..llm.llm_interface import query_llm, clean_llm_response
from ..llm.prompt_manager import PromptManager
from ..config import DEFAULT_CONFIG, LANGUAGE_MAPPING, LLM_CONFIG
from . import text_utils
from ..hooks import hookspecs
from ..system_plugins import default_processors
from .pipeline_objects import Page


logger = logging.getLogger(__name__)

# 在当前模块的顶层初始化一个PromptManager实例
# 在一个完整的应用中，这个实例可能是单例或通过依赖注入传入
prompt_manager = PromptManager()

def _process_page_in_thread(page_data: dict, detected_language: str, pm: pluggy.PluginManager) -> Page:
    """
    在工作线程中执行的函数，用于处理单个页面。
    它调用插件钩子来处理页面，并返回带有处理结果的Page对象。
    """
    page_id = page_data['page_id']
    page_content = page_data['page_content']
    
    # 创建Page对象以传递给插件
    page_object = Page(page_id=page_id, content=page_content)
    
    logger.info(f"  - [Thread] Invoking process_page hook for Page {page_id}...")
    
    # 调用钩子，插件将修改page_object
    pm.hook.process_page(page=page_object, detected_language=detected_language)
    
    return page_object

def get_prompt_with_language(template_name: str, language: str, **kwargs) -> str:
    """
    根据模板名称和语言，生成包含特定语言指令的提示。
    """
    template = prompt_manager.get_template(template_name)
    if not template:
        raise ValueError(f"未找到 '{template_name}' 提示词模板")
    language_instruction = LANGUAGE_MAPPING.get(language, 'English')
    return template.format(language=language_instruction, **kwargs)

def parse_pause_point(response: str) -> Optional[int]:
    """
    从模型对分页任务的响应中解析出断点（pause point）的编号。
    """
    response = clean_llm_response(response)
    try:
        match = re.search(r'Break point:\s*<(\d+)>', response, re.IGNORECASE)
        if match:
            return int(match.group(1))
        match = re.search(r'断点[:：]\s*<(\d+)>', response)
        if match:
            return int(match.group(1))
        match = re.search(r'<(\d+)>', response)
        if match:
            return int(match.group(1))
        patterns = [
            r'(?:break point|断点)[:：]?\s*(\d+)',
            r'(?:选择|choose|select)[:：]?\s*(\d+)',
            r'(?:标签|label|tag)[:：]?\s*(\d+)'
        ]
        for pattern in patterns:
            match = re.search(pattern, response, re.IGNORECASE)
            if match:
                return int(match.group(1))
        return None
    except (ValueError, AttributeError):
        return None

def create_page(
    sentences: List[Dict],
    start_idx: int,
    config: Dict,
    language: str
) -> Tuple[List[Dict], int]:
    """
    从文本片段列表中创建单个页面。
    """
    unit_limit = config.get('unit_limit', 1000)
    start_threshold = config.get('start_threshold', 300)

    page_sentences_with_markers = [sentences[start_idx]['text']]
    unit_count = text_utils.count_text_units(sentences[start_idx]['text'], language)
    j = start_idx + 1

    while unit_count < unit_limit and j < len(sentences):
        segment_units = text_utils.count_text_units(sentences[j]['text'], language)
        unit_count += segment_units

        if unit_count >= start_threshold:
            page_sentences_with_markers.append(f"<{j}>")

        page_sentences_with_markers.append(sentences[j]['text'])
        j += 1

    page_sentences_with_markers.append(f"<{j}>")

    min_units = 100 if language == 'zh' else 50
    if unit_count < min_units:
        return sentences[start_idx:j], j

    try:
        preceding_context = ("" if start_idx == 0 else "...\n" +
            '\n'.join([s['text'] for s in sentences[max(0, start_idx-2):start_idx]]))
        next_preview = "" if j >= len(sentences) else sentences[j]['text'] + "\n..."

        prompt = get_prompt_with_language(
            'pagination',
            language,
            previous_context=preceding_context,
            current_passage='\n'.join(page_sentences_with_markers),
            next_preview=next_preview
        )
        logger.debug(f"create_page - Calling LLM for pagination (start_idx: {start_idx}, j: {j})...")
        response = query_llm(
            prompt,
            temperature=config.get('temperature', 0.0),
            provider=config.get('provider', LLM_CONFIG.get(
                'default_provider', 'ollama'))
        )
        logger.debug(f"create_page - LLM response received for pagination (start_idx: {start_idx}, j: {j}).")

        cleaned_response = clean_llm_response(response)
        pause_point = parse_pause_point(cleaned_response)

        if pause_point and start_idx < pause_point <= j:
            logger.info(f"create_page - Returning page with LLM-determined pause_point: {pause_point}")
            return sentences[start_idx: pause_point], pause_point
        else:
            logger.info(f"create_page - Returning page with default split (LLM response invalid or no valid pause_point).")
            return sentences[start_idx: j], j

    except Exception as e:
        if config.get('verbose', False):
            logger.warning(f"分页决策失败，使用默认切分: {e}")
        logger.error(f"create_page - Exception during LLM call for pagination: {e}. Returning default split.")
        return sentences[start_idx: j], j


def paginate_text(sentences: List[Dict], config: Dict, language: str, storage_manager) -> List[str]:
    """
    对长文本进行分页，将其切分为多个页面，并直接存入数据库。
    返回添加的页面ID列表。
    """
    total_sentences = len(sentences)

    added_page_ids = [] # To collect page IDs added to DB
    i = 0 # Current sentence index
    page_index_in_chapter = 0 # Track page index within the current chapter

    if config.get('verbose', False):
        logger.info(
            f"[分页] 开始为 {total_sentences} 个句子进行分页...")

    while i < total_sentences:
        if config.get('verbose', False):
            progress = (i / total_sentences) * 100 if total_sentences > 0 else 0
            logger.info(
                f"\n[分页] 创建页面 {page_index_in_chapter + 1}，起始句子 {i}/{total_sentences} ({progress:.1f}%)")

        page_sentences, next_i = create_page(
            sentences, i, config, language)
        
        # Derive page_content from page_sentences
        page_content = "\n".join([s['text'] for s in page_sentences])
        
        # Derive start_cfi and end_cfi for the page from the first and last sentence in page_sentences
        page_start_cfi = page_sentences[0]['start_cfi'] if page_sentences else 'N/A'
        page_end_cfi = page_sentences[-1]['end_cfi'] if page_sentences else 'N/A'

        # Get chapter_id from the first sentence in the page
        chapter_id = page_sentences[0]['chapter_id'] if page_sentences else None
        if chapter_id is None:
            logger.info(f"Could not determine chapter_id for page starting at sentence index {i}. Skipping page.")
            i = next_i
            continue

        page_id = storage_manager.add_page_only(
            chapter_id,
            page_index_in_chapter,
            page_content,
            page_start_cfi,
            page_end_cfi
        )
        added_page_ids.append(page_id)
        logger.info(f"    - Added Page {page_index_in_chapter} (ID: {page_id}) to database.")

        if config.get('verbose', False):
            header = f" 页面 {page_index_in_chapter} 内容 "
            logger.debug("-" * 25 + header + "-" * 25)
            logger.debug('\n'.join([s['text'] for s in page_sentences]))
            logger.debug("-" * (50 + len(header)))

        i = next_i
        page_index_in_chapter += 1

    if config.get('verbose', False):
        logger.info(
            f"\n[分页] 完成: 为 {language} 文本创建了 {len(added_page_ids)} 个页面")

    return added_page_ids


def process_book(book_data: dict, storage_manager):
    """处理已解析的书籍数据，并存入数据库。"""
    # 初始化插件管理器
    pm = pluggy.PluginManager("read_agent")
    pm.add_hookspecs(hookspecs)
    pm.register(default_processors)

    # Phase 1: Add book and all chapters (non-LLM dependent)
    logger.info("\n[Step 1/3] Adding book and chapters to database...")
    
    # Calculate file hash to uniquely identify the book's content
    logger.info(f"  - Calculating hash for {book_data['source_path']}...")
    sha256_hash = hashlib.sha256()
    with open(book_data['source_path'], "rb") as f:
        # Read and update hash in chunks of 4K for efficiency
        for byte_block in iter(lambda: f.read(4096), b""):
            sha256_hash.update(byte_block)
    content_hash = sha256_hash.hexdigest()
    logger.info(f"  - Calculated content hash: {content_hash[:16]}...")

    # Language is now detected by the parser and passed in the book_data metadata
    detected_language = book_data['metadata'].get('language', 'en') # Default to 'en' if not found

    book_id = storage_manager.add_book(
        source_path=book_data['source_path'],
        content_hash=content_hash, # Use the real hash
        detected_language=detected_language,
        metadata=book_data['metadata']
    )

    chapter_ids = {}
    all_sentences_in_book = [] # To store all sentences with their chapter_id and sentence_id

    for i, chapter in enumerate(book_data['chapters']):
        if not chapter['content'] or not "".join([s['text'] for s in chapter['content']]).strip(): # Now 'content' holds sentences
            logger.info(f"  - Chapter {i+1} '{chapter['title']}' is empty or has no content, skipping.")
            continue
        
        # Add chapter to DB
        chapter_id = storage_manager.add_chapter_only(
            book_id,
            i, # chapter_index
            chapter['title'],
            "\n".join([s['text'] for s in chapter['content']]), # full_chapter_text from sentences
            chapter['content'][0]['start_cfi'] if chapter['content'] else 'N/A' # cfi_base from first sentence
        )
        chapter_ids[i] = chapter_id
        logger.info(f"  - Added Chapter {i+1} '{chapter['title']}' (ID: {chapter_id}).")

        # Add sentences to DB
        for s_idx, sentence in enumerate(chapter['content']):
            sentence_id = storage_manager.add_sentence_only(
                chapter_id,
                s_idx, # sentence_index_in_chapter
                sentence['text'],
                sentence['start_cfi'],
                sentence['end_cfi']
            )
            all_sentences_in_book.append({
                'sentence_id': sentence_id,
                'chapter_id': chapter_id,
                'text': sentence['text'],
                'start_cfi': sentence['start_cfi'],
                'end_cfi': sentence['end_cfi']
            })
    logger.info("   - Chapters and sentences added.")

    # Phase 2: Paginate all sentences and add pages to DB (LLM dependent)
    logger.info("\n[Step 2/3] Paginating sentences and adding pages to database...")
    
    added_page_ids_for_book = paginate_text(
        all_sentences_in_book, # Pass list of sentence objects
        DEFAULT_CONFIG[detected_language],
        detected_language,
        storage_manager
    )
    
    all_pages_to_process = []
    for page_id in added_page_ids_for_book:
        page_content = storage_manager.get_page_content(page_id) 
        all_pages_to_process.append({
            'page_id': page_id,
            'page_content': page_content,
        })
    logger.info("   - Pages added to database.")

    # Phase 3: Process all pages via plugin hooks concurrently
    logger.info("\n[Step 3/3] Processing pages via plugin hooks (concurrently)...")
    futures = []
    # Use a thread pool to process pages concurrently
    with ThreadPoolExecutor(max_workers=5) as executor:
        for page_data in all_pages_to_process:
            # Submit the processing task to the thread pool
            future = executor.submit(_process_page_in_thread, page_data, detected_language, pm)
            futures.append(future)

        # Process results as they are completed
        for future in as_completed(futures):
            try:
                processed_page_object = future.result()
                page_id = processed_page_object.page_id
                
                if processed_page_object.processed_data:
                    for processor_name, result_data in processed_page_object.processed_data.items():
                        storage_manager.add_processed_data(page_id, processor_name, result_data)
                        logger.info(f"    - [Thread] Saved result from '{processor_name}' for Page {page_id}.")
                else:
                    logger.info(f"    - [Thread] No data processed for Page {page_id}.")
            except Exception as exc:
                logger.error(f'A page processing task generated an exception: {exc}', exc_info=True)


    logger.info("\n--- All processing steps completed ---")
