/* latin-ext */
@font-face {
  font-family: 'Lato';
  font-style: italic;
  font-weight: 400;
  src: local('Lato Italic'), local('Lato-Italic'), url(https://fonts.gstatic.com/s/lato/v15/S6u8w4BMUTPHjxsAUi-qNiXg7eU0.woff2) format('woff2');
  unicode-range: U+0100-024F, U+0259, U+1E00-1EFF, U+2020, U+20A0-20AB, U+20AD-20CF, U+2113, U+2C60-2C7F, U+A720-A7FF;
}
/* latin */
@font-face {
  font-family: 'Lato';
  font-style: italic;
  font-weight: 400;
  src: local('Lato Italic'), local('Lato-Italic'), url(https://fonts.gstatic.com/s/lato/v15/S6u8w4BMUTPHjxsAXC-qNiXg7Q.woff2) format('woff2');
  unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+2000-206F, U+2074, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
}
/* latin-ext */
@font-face {
  font-family: 'Lato';
  font-style: italic;
  font-weight: 700;
  src: local('Lato Bold Italic'), local('Lato-BoldItalic'), url(https://fonts.gstatic.com/s/lato/v15/S6u_w4BMUTPHjxsI5wq_FQftx9897sxZ.woff2) format('woff2');
  unicode-range: U+0100-024F, U+0259, U+1E00-1EFF, U+2020, U+20A0-20AB, U+20AD-20CF, U+2113, U+2C60-2C7F, U+A720-A7FF;
}
/* latin */
@font-face {
  font-family: 'Lato';
  font-style: italic;
  font-weight: 700;
  src: local('Lato Bold Italic'), local('Lato-BoldItalic'), url(https://fonts.gstatic.com/s/lato/v15/S6u_w4BMUTPHjxsI5wq_Gwftx9897g.woff2) format('woff2');
  unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+2000-206F, U+2074, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
}
/* latin-ext */
@font-face {
  font-family: 'Lato';
  font-style: normal;
  font-weight: 400;
  src: local('Lato Regular'), local('Lato-Regular'), url(https://fonts.gstatic.com/s/lato/v15/S6uyw4BMUTPHjxAwXiWtFCfQ7A.woff2) format('woff2');
  unicode-range: U+0100-024F, U+0259, U+1E00-1EFF, U+2020, U+20A0-20AB, U+20AD-20CF, U+2113, U+2C60-2C7F, U+A720-A7FF;
}
/* latin */
@font-face {
  font-family: 'Lato';
  font-style: normal;
  font-weight: 400;
  src: local('Lato Regular'), local('Lato-Regular'), url(https://fonts.gstatic.com/s/lato/v15/S6uyw4BMUTPHjx4wXiWtFCc.woff2) format('woff2');
  unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+2000-206F, U+2074, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
}
/* latin-ext */
@font-face {
  font-family: 'Lato';
  font-style: normal;
  font-weight: 700;
  src: local('Lato Bold'), local('Lato-Bold'), url(https://fonts.gstatic.com/s/lato/v15/S6u9w4BMUTPHh6UVSwaPGQ3q5d0N7w.woff2) format('woff2');
  unicode-range: U+0100-024F, U+0259, U+1E00-1EFF, U+2020, U+20A0-20AB, U+20AD-20CF, U+2113, U+2C60-2C7F, U+A720-A7FF;
}
/* latin */
@font-face {
  font-family: 'Lato';
  font-style: normal;
  font-weight: 700;
  src: local('Lato Bold'), local('Lato-Bold'), url(https://fonts.gstatic.com/s/lato/v15/S6u9w4BMUTPHh6UVSwiPGQ3q5d0.woff2) format('woff2');
  unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+2000-206F, U+2074, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
}

a {
  color: #1772d0;
  text-decoration: none;
}

a:focus,
a:hover {
  color: #f09228;
  text-decoration: none;
}

body,
td,
th,
tr,
p,
a {
  font-family: 'Lato', Verdana, Helvetica, Calibri;
}

strong {
  font-family: 'Lato', Verdana, Helvetica, Calibri;
  font-size: 14px;
}

heading {
  font-family: 'Lato', Verdana, Helvetica, Calibri;
  font-size: 22px;
}

papertitle {
  font-family: 'Lato', Verdana, Helvetica, Calibri;
  font-size: 14px;
  font-weight: 700
}

name {
  font-family: 'Lato', Verdana, Helvetica, Calibri;
  font-size: 32px;
}

.one {
  width: 160px;
  height: 160px;
  position: relative;
}

.two {
  width: 160px;
  height: 160px;
  position: absolute;
  transition: opacity .2s ease-in-out;
  -moz-transition: opacity .2s ease-in-out;
  -webkit-transition: opacity .2s ease-in-out;
}

.fade {
  transition: opacity .2s ease-in-out;
  -moz-transition: opacity .2s ease-in-out;
  -webkit-transition: opacity .2s ease-in-out;
}

span.highlight {
  background-color: #ffffd0;
}

.CodeMirror {
    font-size: .8em;
    height: auto;
}

.CodeMirror-scroll {
    overflow-y: hidden;
    overflow-x: auto;
}

#header_img {
    margin-top: 2em;
    margin-bottom: 1em;
}

.list-inline {
	list-style: none;
	margin-left: -0.5em;
	margin-right: -0.5em;
	padding-left: 0;
}

.list-inline > li {
	display: inline-block;
	margin-left: 0.5em;
	margin-right: 0.5em;
}

.img-responsive {
    margin: 0 auto;
}

#imgtext {
  color: rgb(120, 120, 120);
}

/* #answer {
  color: black;
  background-color: #fce5cd;
  font-family: monospace;
} */


.carousel .carousel-indicators button {
  border-radius: 50%;
  width: 12px;
  height: 12px;
  margin-right: 3px;
  margin-left: 3px;
}

.carousel-dark .carousel-control-next-icon, .carousel-dark .carousel-control-prev-icon {
  filter: invert(0) grayscale(100);
}

.carousel-control-next-icon, .carousel-control-prev-icon {
  display: inline-block;
  width: 4rem;
  height: 4rem;
  background-repeat: no-repeat;
  background-position: 50%;
  background-size: 100% 100%;
}

.carousel-container {
  width: 100%;
  margin: 16px 0px;
  padding-bottom: 16px;
  overflow-x: auto;
  display: flex;
  width: 100%;
  gap: 16px;
  align-items: center;
  scroll-snap-type: x mandatory;
}

.carousel-container::-webkit-scrollbar {
  height: 14px;
  width: calc(100% - 48px);
}

.carousel-container::-webkit-scrollbar-track {
  background: #eee;
  border-radius: 8px;
  margin-left: 100px;
  margin-right: 100px;
}

.carousel-container::-webkit-scrollbar-thumb {
  background: #ccc;
  border-radius: 8px;
}

.carousel-container::-webkit-scrollbar-thumb:hover {
  background: #999;
}

.carousel-slide {
  border-radius: 10px;
  flex: 1 0 30%;
  height: 320px;
  flex-flow: column nowrap;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: #bae;
  scroll-snap-align: center;
}

.carousel-slide video {
  border-radius: 10px;
}

@media (max-width: 600px) {
  .carousel-slide {
    flex: 1 0 90%;
  }
}
