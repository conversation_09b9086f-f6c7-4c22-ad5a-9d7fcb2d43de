#!/usr/bin/env python3
"""
Read Agent 测试脚本

这个脚本用于测试 Read Agent 的基本功能。
注意：需要本地模型API运行才能执行完整测试。
"""

import sys
import os

# 添加当前目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

try:
    import read_agent
    print("✓ Read Agent 模块导入成功")
except ImportError as e:
    print(f"✗ Read Agent 模块导入失败: {e}")
    sys.exit(1)

def test_basic_functions():
    """测试基本功能"""
    print("\n=== 测试基本功能 ===")
    
    # 测试文本解析
    test_text = "This is a test.\n\nThis is another paragraph.\n\nAnd a third one."
    parsed = read_agent.parse_text(test_text)
    print(f"✓ 文本解析: {len(parsed)} 字符")
    
    # 测试词数统计
    word_count = read_agent.count_words(test_text)
    print(f"✓ 词数统计: {word_count} 词")
    
    # 测试统计信息
    stats = read_agent.get_text_statistics(test_text)
    print(f"✓ 统计信息: {stats}")
    
    # 测试配置创建
    config = read_agent.create_model_config()
    print(f"✓ 配置创建: {config['model']}")

def test_parsing_functions():
    """测试解析功能"""
    print("\n=== 测试解析功能 ===")
    
    # 测试暂停点解析
    response1 = "Break point: <57>\nBecause this is a good place to stop."
    pause_point = read_agent.parse_pause_point(response1)
    print(f"✓ 暂停点解析: {pause_point}")
    
    # 测试页面ID解析
    response2 = "I want to look up pages [1, 3, 5] to find more information."
    page_ids = read_agent.parse_page_ids(response2)
    print(f"✓ 页面ID解析: {page_ids}")

def test_offline_processing():
    """测试离线处理功能（不需要API）"""
    print("\n=== 测试离线处理功能 ===")
    
    sample_text = """
    Artificial Intelligence has revolutionized many industries. 
    Machine learning algorithms can now process vast amounts of data.
    
    In healthcare, AI helps with medical diagnosis and drug discovery.
    Autonomous vehicles use AI for navigation and safety.
    
    However, there are ethical concerns about AI bias and job displacement.
    The future requires careful consideration of AI's societal impact.
    """
    
    # 测试文本分页（模拟，不调用API）
    config = {
        'word_limit': 50,
        'start_threshold': 20,
        'verbose': True
    }
    
    try:
        # 这会尝试调用API，但我们可以测试到分页逻辑
        print("注意：以下测试需要本地模型API运行")
        print("如果API不可用，将显示错误信息")
        
        # 测试要点记忆上下文构建
        gist_memories = [
            "AI has revolutionized industries with ML algorithms",
            "Healthcare and autonomous vehicles benefit from AI",
            "Ethical concerns exist about bias and job displacement"
        ]
        
        gist_context = read_agent._build_gist_context(gist_memories)
        print(f"✓ 要点记忆上下文构建: {len(gist_context)} 字符")
        
        # 测试扩展上下文构建
        pages = [
            ["AI has revolutionized many industries.", "ML algorithms process data."],
            ["Healthcare uses AI for diagnosis.", "Autonomous vehicles use AI."],
            ["Ethical concerns about AI bias.", "Job displacement is a concern."]
        ]
        
        expanded_context = read_agent._build_expanded_context(
            gist_memories, pages, [0, 2]
        )
        print(f"✓ 扩展上下文构建: {len(expanded_context)} 字符")
        
    except Exception as e:
        print(f"注意：API相关功能需要本地模型服务: {e}")

def test_persistence():
    """测试持久化功能"""
    print("\n=== 测试持久化功能 ===")
    
    # 创建测试数据
    test_result = {
        'response': 'This is a test response',
        'compression_rate': 0.75,
        'relevant_pages': [0, 1, 2],
        'statistics': {'total_words': 100}
    }
    
    try:
        # 测试保存和加载结果
        read_agent.save_result(test_result, 'test_result.json')
        loaded_result = read_agent.load_result('test_result.json')
        print("✓ 结果保存和加载成功")
        
        # 测试保存处理过的文档
        test_text = "This is a test document."
        test_pages = [["This is a test document."]]
        test_gists = ["Test document summary"]
        
        read_agent.save_processed_document(
            test_text, test_pages, test_gists, 'test_processed.pkl'
        )
        
        loaded_text, loaded_pages, loaded_gists = read_agent.load_processed_document(
            'test_processed.pkl'
        )
        print("✓ 处理文档保存和加载成功")
        
        # 清理测试文件
        import os
        for file in ['test_result.json', 'test_processed.pkl']:
            if os.path.exists(file):
                os.remove(file)
        print("✓ 测试文件清理完成")
        
    except Exception as e:
        print(f"✗ 持久化测试失败: {e}")

def main():
    """主测试函数"""
    print("Read Agent 功能测试")
    print("=" * 50)
    
    test_basic_functions()
    test_parsing_functions()
    test_offline_processing()
    test_persistence()
    
    print("\n=== 测试完成 ===")
    print("注意：完整功能测试需要本地模型API运行")
    print("请确保您的本地模型服务器可访问后再进行完整测试")
    
    print("\n使用示例：")
    print("import read_agent")
    print("result = read_agent.process_long_text(text, query)")
    print("print(result['response'])")

if __name__ == "__main__":
    main()
