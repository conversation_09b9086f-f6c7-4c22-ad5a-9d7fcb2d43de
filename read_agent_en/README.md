# Read Agent - English Long Text Processing

A Python implementation of the Read Agent algorithm for processing long English texts using episode pagination, gist memory generation, and intelligent lookup mechanisms.

Based on the paper: **"A Human-Inspired Reading Agent with Gist Memory of Very Long Contexts"** by <PERSON> et al. (2024)

## 📁 File Structure

```
read_agent_en/
├── read_agent.py                    # Main algorithm implementation
├── README.md                        # This file
├── README_read_agent.md            # Detailed usage documentation
├── running_for_governer.txt        # Test document (<PERSON>'s story)
├── test_read_agent.py              # Basic functionality tests
├── test_running_for_governor.py    # Comprehensive test suite
└── simple_test.py                  # Simple demonstration script
```

## 🚀 Quick Start

### 1. Basic Usage

```python
import read_agent

# Load your long document
text = """
Your very long document content here...
This can be thousands of words long.
"""

# Ask a question
query = "What are the main points discussed in this document?"

# Process the text
result = read_agent.process_long_text(
    text=text,
    query=query,
    api_url="http://your-local-api:1234/v1/chat/completions",
    verbose=True
)

# View the result
print("Answer:", result['response'])
print("Compression Rate:", f"{result['compression_rate']:.2%}")
```

### 2. Test with <PERSON> Twain's "Running for Governor"

```bash
# Run the simple test
python simple_test.py

# Run comprehensive tests
python test_running_for_governor.py

# Run basic functionality tests
python test_read_agent.py
```

## ⚙️ Configuration

### Local Model API Setup

The algorithm requires a local LLM API. Default configuration:

```python
{
    'api_url': 'http://************:1234/v1/chat/completions',
    'model': 'qwen3-30b-a3b',
    'api_key': 'lm_studio'
}
```

### Key Parameters

- `word_limit`: Maximum words per page (default: 600)
- `start_threshold`: Word threshold to start pagination decisions (default: 280)
- `max_lookup_pages`: Maximum pages to lookup (default: 5)
- `temperature`: Model temperature (default: 0.0)
- `verbose`: Enable detailed logging (default: False)

## 🧠 How It Works

1. **Episode Pagination**: Intelligently splits long text into logical segments
2. **Gist Memory Generation**: Creates concise summaries for each segment
3. **Intelligent Lookup**: Selects relevant segments based on the query
4. **Context Expansion**: Combines summaries with detailed content
5. **Response Generation**: Produces structured natural language answers

## 📊 Features

- ✅ **Procedural Programming**: Pure function-based design
- ✅ **Local API Support**: Works with local LLM servers
- ✅ **Persistence**: Save and load processing results
- ✅ **Batch Processing**: Handle multiple queries efficiently
- ✅ **Error Handling**: Robust error recovery mechanisms
- ✅ **Compression**: Achieve 70-90% text compression rates

## 📖 Test Document

The included `running_for_governer.txt` contains Mark Twain's classic short story "Running for Governor" (1870), a humorous satire about political campaigns. This public domain text serves as an excellent test case for the algorithm.

**Document Stats:**
- 1,774 words
- 162 paragraphs
- Classic American literature
- Rich narrative structure

## 🔧 Dependencies

```python
# Required packages
import json
import pickle
import re
import time
import datetime
import os
import requests
from typing import List, Dict, Optional, Tuple, Union
```

## 📝 Example Output

When asked "What accusations were made against Mark Twain during his campaign?", the algorithm produces a detailed, structured response covering:

- Perjury accusations
- Theft allegations
- Character defamation
- Fabricated incidents
- Political sabotage tactics

The response demonstrates the algorithm's ability to:
- Extract specific details from long text
- Organize information logically
- Provide comprehensive answers
- Reference source material accurately

## 🎯 Use Cases

- **Academic Research**: Analyze long papers and documents
- **Literature Study**: Examine novels, stories, and essays
- **Document Analysis**: Process reports, articles, and manuscripts
- **Content Summarization**: Extract key information from lengthy texts
- **Question Answering**: Get detailed answers about document content

## 🚨 Requirements

1. **Local LLM API**: Ensure your local model server is running
2. **Network Access**: API endpoint must be accessible
3. **Python 3.7+**: Compatible with modern Python versions
4. **Memory**: Sufficient RAM for document processing

## 📈 Performance

Tested with "Running for Governor":
- **Processing Time**: ~2 minutes
- **Compression Rate**: 84.27%
- **Accuracy**: High-quality, detailed responses
- **Memory Usage**: Efficient with persistence support

## 🔍 Troubleshooting

1. **API Connection Issues**: Check your local model server status
2. **Import Errors**: Ensure all dependencies are installed
3. **Memory Issues**: Use persistence for large documents
4. **Slow Processing**: Adjust `word_limit` and `max_lookup_pages`

## 📚 Further Reading

- See `README_read_agent.md` for detailed documentation
- Check test files for usage examples
- Review the original paper for algorithm details

---

**Note**: This implementation focuses on English text processing and requires a local LLM API for full functionality.
