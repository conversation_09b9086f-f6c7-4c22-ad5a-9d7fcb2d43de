# Read Agent - 长文本处理算法

基于论文 "A Human-Inspired Reading Agent with Gist Memory of Very Long Contexts" 的Python实现。

## 功能特点

- **智能分页**：自动将长文本分割成逻辑片段
- **要点记忆**：为每个片段生成简洁的要点摘要
- **智能查找**：基于查询内容智能选择相关片段
- **自然语言回答**：生成结构化的自然语言响应
- **持久化支持**：保存和加载处理结果，避免重复计算

## 安装要求

```python
# 必需的Python包
pip install requests
```

## 基本使用

```python
import read_agent

# 准备长文本
text = """
Your very long document content here...
This can be thousands of words long.
"""

# 提出查询
query = "What are the main points discussed in this document?"

# 处理文本
result = read_agent.process_long_text(
    text=text,
    query=query,
    api_url="http://localhost:1234/v1/chat/completions",  # 本地模型API
    verbose=True
)

# 查看结果
print("Response:", result['response'])
print("Compression Rate:", f"{result['compression_rate']:.2%}")
print("Pages Used:", result['relevant_pages'])
```

## 高级功能

### 1. 保存和加载处理结果

```python
# 保存处理结果
result = read_agent.process_long_text(
    text=text,
    query=query,
    save_path="my_analysis"  # 会生成 my_analysis_processed.pkl 和 my_analysis_result.json
)

# 后续查询使用已处理的文档
result2 = read_agent.process_long_text(
    text=text,
    query="Another question about the same document",
    load_processed="my_analysis_processed.pkl"  # 跳过分页和要点生成
)
```

### 2. 批量查询

```python
queries = [
    "What are the main topics?",
    "What are the key conclusions?",
    "What methodology was used?"
]

results = read_agent.batch_query(
    text=text,
    queries=queries,
    save_base_path="batch_analysis",
    verbose=True
)
```

### 3. 直接处理文件

```python
result = read_agent.process_text_file(
    file_path="document.txt",
    query="Summarize the main arguments",
    verbose=True
)
```

## 配置参数

| 参数 | 默认值 | 说明 |
|------|--------|------|
| `word_limit` | 600 | 每页最大词数 |
| `start_threshold` | 280 | 开始分页决策的词数阈值 |
| `max_lookup_pages` | 5 | 最大查找页面数 |
| `temperature` | 0.0 | 模型温度参数 |
| `verbose` | False | 是否输出详细信息 |

## 本地模型配置

确保您的本地模型API正在运行：

```bash
# 例如使用 LM Studio
# 启动本地API服务器在 http://localhost:1234
```

默认配置：
- API地址：`http://************:1234/v1/chat/completions`
- 模型：`qwen3-30b-a3b`
- API密钥：`lm_studio`

## 输出格式

```python
{
    'pages': List[List[str]],           # 分页结果
    'gist_memories': List[str],         # 要点记忆
    'relevant_pages': List[int],        # 相关页面索引
    'expanded_context': str,            # 扩展上下文
    'response': str,                    # 最终响应
    'compression_rate': float,          # 压缩率
    'statistics': dict                  # 文本统计信息
}
```

## 算法流程

1. **文本分页**：将长文本智能分割成逻辑片段
2. **要点生成**：为每个片段生成简洁的要点记忆
3. **相关性查找**：基于查询确定需要详细查看的页面
4. **上下文构建**：结合要点记忆和详细内容
5. **响应生成**：基于完整上下文生成自然语言回答

## 注意事项

- 需要本地大语言模型API支持
- 适用于英文长文本处理
- 文本长度建议在1000-50000词之间
- 确保API服务器稳定运行

## 错误处理

算法包含完善的错误处理机制：
- API调用失败时自动重试
- 分页失败时使用默认策略
- 要点生成失败时使用文本截断
- 详细的错误日志输出

## 性能优化

- 使用持久化避免重复处理
- 批量查询共享分页和要点记忆
- 智能的页面选择减少API调用
- 可配置的参数适应不同场景
