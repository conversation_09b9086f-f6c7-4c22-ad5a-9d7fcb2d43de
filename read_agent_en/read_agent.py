"""
Read Agent: A Human-Inspired Reading Agent with Gist Memory of Very Long Contexts

This module implements the Read Agent algorithm for processing long English texts
using episode pagination, gist memory generation, and intelligent lookup mechanisms.

Based on the paper: "A Human-Inspired Reading Agent with Gist Memory of Very Long Contexts"
by <PERSON> et al. (2024)

Usage:
    import read_agent
    
    text = "Your long document content..."
    query = "What are the main points discussed?"
    
    result = read_agent.process_long_text(text, query)
    print(result['response'])
"""

import json
import pickle
import re
import time
import datetime
import os
from typing import List, Dict, Optional, Tuple, Union
import requests

# ==================== 1. 配置和常量 ====================

DEFAULT_CONFIG = {
    'word_limit': 600,
    'start_threshold': 280,
    'max_lookup_pages': 5,
    'temperature': 0.0,
    'max_retries': 3,
    'verbose': False,
    'api_url': 'http://************:1234/v1/chat/completions',
    'api_key': 'lm_studio',
    'model': 'qwen3-30b-a3b',
    'max_tokens': 4096
}

PROMPT_TEMPLATES = {
    'pagination': """You are given a passage that is taken from a larger text (article, book, ...) and some numbered labels between the paragraphs in the passage.
Numbered label are in angeled brackets. For example, if the label number is 19, it shows as <19> in text.
Please choose one label that it is natural to break reading.
Such point can be scene transition, end of a dialogue, end of an argument, narrative transition, etc.
Please answer the break point label and explain.
For example, if <57> is a good point to break, answer with "Break point: <57>
Because ..."

Previous context: {previous_context}

Current passage:
{current_passage}

Next preview: {next_preview}""",

    'gisting': """Please shorten the following passage while preserving the key information.
Just give me a shortened version. DO NOT explain your reason.

Passage:
{page_text}""",

    'lookup': """Based on the following document summary and user query, determine which pages would be most relevant to review for answering the query.

Document Summary:
{gist_context}

User Query: {query}

Please respond with the page numbers you want to review in format: [1, 3, 5]
Explain your reasoning briefly.""",

    'response': """Based on the following context from a long document, please provide a comprehensive and well-structured answer to the user's query.

Context:
{context}

User Query: {query}

Instructions:
- Provide a detailed, informative response
- Structure your answer clearly with headings or bullet points when appropriate  
- Reference the document content explicitly
- Ensure accuracy and avoid speculation beyond what's stated in the context
- If the context doesn't contain enough information, state this clearly

Answer:"""
}

# ==================== 2. 工具函数 ====================


def count_words(text: str) -> int:
    """简单的词数统计"""
    return len(text.split())


def parse_text(raw_text: str) -> str:
    """
    解析文本，基于QuALITY数据集的解析器
    """
    lines = []
    previous_line = None
    for i, line in enumerate(raw_text.split('\n')):
        line = line.strip()
        original_line = line
        if line == '':
            if previous_line == '':
                line = '\n'
            else:
                previous_line = original_line
                continue
        previous_line = original_line
        lines.append(line)
    return ' '.join(lines)


def parse_pause_point(response: str) -> Optional[int]:
    """
    从模型响应中解析暂停点
    """
    try:
        # 查找 "Break point: <数字>" 模式
        match = re.search(r'Break point:\s*<(\d+)>', response)
        if match:
            return int(match.group(1))

        # 查找 "<数字>" 模式
        match = re.search(r'<(\d+)>', response)
        if match:
            return int(match.group(1))

        return None
    except (ValueError, AttributeError):
        return None


def parse_page_ids(response: str) -> List[int]:
    """
    从模型响应中解析页面ID列表
    """
    page_ids = []
    try:
        # 查找 [数字, 数字, ...] 模式
        match = re.search(r'\[([^\]]+)\]', response)
        if match:
            ids_str = match.group(1).split(',')
            for id_str in ids_str:
                id_str = id_str.strip()
                if id_str.isdigit():
                    page_ids.append(int(id_str))

        # 如果没找到，尝试查找单独的数字
        if not page_ids:
            numbers = re.findall(r'\b(\d+)\b', response)
            page_ids = [int(num) for num in numbers[:5]]  # 限制最多5个

    except (ValueError, AttributeError):
        pass

    return page_ids


def get_text_statistics(text: str) -> Dict:
    """
    获取文本统计信息
    """
    return {
        'total_words': count_words(text),
        'total_characters': len(text),
        'total_lines': len(text.split('\n')),
        'paragraphs': len([p for p in text.split('\n') if p.strip()])
    }


def format_results(result: Dict, include_details: bool = False) -> str:
    """
    格式化结果输出
    """
    output = []
    output.append(f"Response: {result['response']}")
    output.append(f"Compression Rate: {result['compression_rate']:.2%}")
    output.append(f"Pages Used: {result['relevant_pages']}")

    if include_details:
        output.append(f"Total Pages: {len(result['pages'])}")
        output.append(f"Statistics: {result['statistics']}")

    return '\n'.join(output)


# ==================== 3. 本地模型接口 ====================

def query_local_model(
    prompt: str,
    api_url: str = 'http://************:1234/v1/chat/completions',
    api_key: str = 'lm_studio',
    model: str = 'qwen3-30b-a3b',
    temperature: float = 0.0,
    max_tokens: int = 4096,
    max_retries: int = 5
) -> str:
    """
    查询本地模型（基于notebook中的实现）

    Args:
        prompt: 输入提示
        api_url: API地址
        api_key: API密钥
        model: 模型名称
        temperature: 温度参数
        max_tokens: 最大token数
        max_retries: 最大重试次数

    Returns:
        模型响应文本
    """
    headers = {
        'Authorization': f'Bearer {api_key}',
        'Content-Type': 'application/json'
    }

    payload = {
        'model': model,
        'messages': [{'role': 'user', 'content': prompt}],
        'temperature': temperature,
        'max_tokens': max_tokens
    }

    for attempt in range(max_retries):
        try:
            response = requests.post(
                api_url, headers=headers, json=payload, timeout=60)
            response.raise_for_status()
            data = response.json()
            return data['choices'][0]['message']['content']

        except requests.exceptions.Timeout:
            print(f'{datetime.datetime.now()}: query_local_model: 请求超时')
            if attempt < max_retries - 1:
                time.sleep(5)
        except requests.exceptions.HTTPError as e:
            if e.response.status_code == 429:
                print(f'{datetime.datetime.now()}: query_local_model: 请求过于频繁: {e}')
                time.sleep(30)
            else:
                print(f'{datetime.datetime.now()}: query_local_model: HTTP错误: {e}')
                if attempt < max_retries - 1:
                    time.sleep(5)
        except Exception as e:
            print(f'{datetime.datetime.now()}: query_local_model: 未知错误: {e}')
            if attempt < max_retries - 1:
                time.sleep(5)

    raise Exception(f'达到最大重试次数({max_retries})仍未能成功请求API')


def create_model_config(
    api_url: str = 'http://************:1234/v1/chat/completions',
    model: str = 'qwen3-30b-a3b',
    api_key: str = 'lm_studio'
) -> Dict:
    """
    创建模型配置字典
    """
    return {
        'api_url': api_url,
        'model': model,
        'api_key': api_key
    }


# ==================== 4. 核心算法函数 ====================

def _create_page(
    paragraphs: List[str],
    start_idx: int,
    word_limit: int,
    start_threshold: int,
    config: Dict
) -> Tuple[List[str], int]:
    """
    创建单个页面
    """
    passage = [paragraphs[start_idx]]
    wcount = count_words(paragraphs[start_idx])
    j = start_idx + 1

    # 添加段落直到达到词数限制
    while wcount < word_limit and j < len(paragraphs):
        wcount += count_words(paragraphs[j])
        if wcount >= start_threshold:
            passage.append(f"<{j}>")
        passage.append(paragraphs[j])
        j += 1

    passage.append(f"<{j}>")

    # 如果页面太短，直接返回
    if wcount < 350:
        return paragraphs[start_idx:j], j

    # 使用模型决定分页点
    preceding = "" if start_idx == 0 else "...\n" + \
        '\n'.join(paragraphs[max(0, start_idx-2):start_idx])
    end_tag = "" if j >= len(paragraphs) else paragraphs[j] + "\n..."

    prompt = PROMPT_TEMPLATES['pagination'].format(
        previous_context=preceding,
        current_passage='\n'.join(passage),
        next_preview=end_tag
    )

    try:
        response = query_local_model(
            prompt,
            api_url=config['api_url'],
            api_key=config['api_key'],
            model=config['model'],
            temperature=config.get('temperature', 0.0)
        )
        pause_point = parse_pause_point(response)

        if pause_point and start_idx < pause_point <= j:
            return paragraphs[start_idx:pause_point], pause_point
        else:
            return paragraphs[start_idx:j], j

    except Exception as e:
        if config.get('verbose', False):
            print(f"分页决策失败，使用默认分割: {e}")
        return paragraphs[start_idx:j], j


def paginate_text(text: str, config: Dict) -> List[List[str]]:
    """
    将文本分割成逻辑页面

    Args:
        text: 输入文本
        config: 配置字典

    Returns:
        页面列表，每个页面是段落列表
    """
    # 解析文本
    parsed_text = parse_text(text)
    paragraphs = parsed_text.split('\n')

    pages = []
    i = 0
    word_limit = config.get('word_limit', 600)
    start_threshold = config.get('start_threshold', 280)

    while i < len(paragraphs):
        page, next_i = _create_page(
            paragraphs, i, word_limit, start_threshold, config
        )
        pages.append(page)
        i = next_i

    if config.get('verbose', False):
        print(f"[Pagination] Created {len(pages)} pages")

    return pages


def generate_gist_memories(pages: List[List[str]], config: Dict) -> List[str]:
    """
    为每个页面生成要点记忆

    Args:
        pages: 页面列表
        config: 配置字典

    Returns:
        要点记忆列表
    """
    gist_memories = []

    for i, page in enumerate(pages):
        page_text = '\n'.join(page)
        prompt = PROMPT_TEMPLATES['gisting'].format(page_text=page_text)

        try:
            response = query_local_model(
                prompt,
                api_url=config['api_url'],
                api_key=config['api_key'],
                model=config['model'],
                temperature=config.get('temperature', 0.0)
            )
            gist_memory = response.strip()
            gist_memories.append(gist_memory)

            if config.get('verbose', False):
                print(f"[Gist] Page {i}: {gist_memory}")

        except Exception as e:
            if config.get('verbose', False):
                print(f"生成要点记忆失败，使用原文摘要: {e}")
            # 使用简单的文本截断作为后备
            gist_memory = page_text[:200] + \
                "..." if len(page_text) > 200 else page_text
            gist_memories.append(gist_memory)

    return gist_memories


def _build_gist_context(gist_memories: List[str]) -> str:
    """
    构建要点记忆上下文
    """
    gist_context = []
    for i, gist in enumerate(gist_memories):
        gist_context.append(f"<Page {i}>\n{gist}")
    return '\n\n'.join(gist_context)


def find_relevant_pages(gist_memories: List[str], query: str, config: Dict) -> List[int]:
    """
    基于查询找到相关页面

    Args:
        gist_memories: 要点记忆列表
        query: 用户查询
        config: 配置字典

    Returns:
        相关页面索引列表
    """
    # 构建要点记忆上下文
    gist_context = _build_gist_context(gist_memories)

    # 构建查找提示
    prompt = PROMPT_TEMPLATES['lookup'].format(
        gist_context=gist_context,
        query=query
    )

    try:
        # 查询模型
        response = query_local_model(
            prompt,
            api_url=config['api_url'],
            api_key=config['api_key'],
            model=config['model'],
            temperature=config.get('temperature', 0.0)
        )

        # 解析页面ID
        page_ids = parse_page_ids(response)

        # 验证页面ID有效性
        valid_page_ids = []
        for page_id in page_ids:
            if 0 <= page_id < len(gist_memories):
                valid_page_ids.append(page_id)

        # 限制查找页面数量
        max_pages = config.get('max_lookup_pages', 5)
        valid_page_ids = valid_page_ids[:max_pages]

        if config.get('verbose', False):
            print(f"[Lookup] Selected pages: {valid_page_ids}")

        return valid_page_ids

    except Exception as e:
        if config.get('verbose', False):
            print(f"页面查找失败，使用前几页: {e}")
        # 后备策略：返回前几页
        max_pages = min(config.get('max_lookup_pages', 5), len(gist_memories))
        return list(range(max_pages))


def _build_expanded_context(
    gist_memories: List[str],
    pages: List[List[str]],
    relevant_page_ids: List[int]
) -> str:
    """
    构建扩展上下文
    """
    context_parts = []

    # 添加所有要点记忆作为背景
    gist_context = _build_gist_context(gist_memories)
    context_parts.append("Document Summary:")
    context_parts.append(gist_context)
    context_parts.append("\nDetailed Content from Relevant Pages:")

    # 添加相关页面的详细内容
    for page_id in relevant_page_ids:
        if 0 <= page_id < len(pages):
            page_content = '\n'.join(pages[page_id])
            context_parts.append(f"\n--- Page {page_id} (Detailed) ---")
            context_parts.append(page_content)

    return '\n'.join(context_parts)


def generate_response(context: str, query: str, config: Dict) -> str:
    """
    基于上下文生成响应

    Args:
        context: 扩展上下文
        query: 用户查询
        config: 配置字典

    Returns:
        生成的响应
    """
    prompt = PROMPT_TEMPLATES['response'].format(
        context=context,
        query=query
    )

    try:
        response = query_local_model(
            prompt,
            api_url=config['api_url'],
            api_key=config['api_key'],
            model=config['model'],
            temperature=config.get('temperature', 0.0)
        )
        return response.strip()

    except Exception as e:
        if config.get('verbose', False):
            print(f"响应生成失败: {e}")
        return f"Sorry, I encountered an error while processing your query: {str(e)}"


# ==================== 5. 主要接口函数 ====================

def process_long_text(
    text: str,
    query: str,
    api_url: str = 'http://************:1234/v1/chat/completions',
    model: str = 'qwen3-30b-a3b',
    api_key: str = 'lm_studio',
    word_limit: int = 600,
    start_threshold: int = 280,
    max_lookup_pages: int = 5,
    temperature: float = 0.0,
    verbose: bool = False,
    save_path: str = None,
    load_processed: str = None
) -> Dict:
    """
    处理长文本的主要接口

    Args:
        text: 输入的长文本
        query: 用户查询（问题、任务描述等）
        api_url: 本地模型API地址
        model: 模型名称
        api_key: API密钥
        word_limit: 分页词数限制
        start_threshold: 开始分页的阈值
        max_lookup_pages: 最大查找页面数
        temperature: 模型温度参数
        verbose: 是否输出详细信息
        save_path: 保存结果的路径（可选）
        load_processed: 加载已处理文档的路径（可选）

    Returns:
        包含处理结果的字典:
        {
            'pages': List[List[str]],           # 分页结果
            'gist_memories': List[str],         # 要点记忆
            'relevant_pages': List[int],        # 相关页面索引
            'expanded_context': str,            # 扩展上下文
            'response': str,                    # 最终响应
            'compression_rate': float,          # 压缩率
            'statistics': dict                  # 统计信息
        }
    """

    # 构建配置
    config = {
        'api_url': api_url,
        'model': model,
        'api_key': api_key,
        'word_limit': word_limit,
        'start_threshold': start_threshold,
        'max_lookup_pages': max_lookup_pages,
        'temperature': temperature,
        'verbose': verbose
    }

    # 尝试加载已处理的文档
    if load_processed and os.path.exists(load_processed):
        try:
            _, pages, gist_memories = load_processed_document(load_processed)
            if verbose:
                print(
                    f"[Load] Loaded processed document from {load_processed}")
        except Exception as e:
            if verbose:
                print(f"[Load] Failed to load processed document: {e}")
            pages = None
            gist_memories = None
    else:
        pages = None
        gist_memories = None

    # 如果没有加载成功，进行正常处理
    if pages is None or gist_memories is None:
        if verbose:
            print("[Process] Starting text processing...")

        # 1. 文本分页
        pages = paginate_text(text, config)

        # 2. 生成要点记忆
        gist_memories = generate_gist_memories(pages, config)

        # 保存处理过的文档
        if save_path:
            try:
                save_processed_document(
                    text, pages, gist_memories, f"{save_path}_processed.pkl")
                if verbose:
                    print(
                        f"[Save] Saved processed document to {save_path}_processed.pkl")
            except Exception as e:
                if verbose:
                    print(f"[Save] Failed to save processed document: {e}")

    # 3. 基于查询找到相关页面
    relevant_pages = find_relevant_pages(gist_memories, query, config)

    # 4. 构建扩展上下文
    expanded_context = _build_expanded_context(
        gist_memories, pages, relevant_pages)

    # 5. 生成响应
    response = generate_response(expanded_context, query, config)

    # 计算压缩率
    original_text_words = count_words(text)
    gist_text_words = sum(count_words(gist) for gist in gist_memories)
    compression_rate = 1.0 - \
        (gist_text_words / original_text_words) if original_text_words > 0 else 0.0

    # 构建结果
    result = {
        'pages': pages,
        'gist_memories': gist_memories,
        'relevant_pages': relevant_pages,
        'expanded_context': expanded_context,
        'response': response,
        'compression_rate': compression_rate,
        'statistics': get_text_statistics(text)
    }

    # 保存完整结果
    if save_path:
        try:
            save_result(result, f"{save_path}_result.json")
            if verbose:
                print(f"[Save] Saved result to {save_path}_result.json")
        except Exception as e:
            if verbose:
                print(f"[Save] Failed to save result: {e}")

    return result


# ==================== 6. 持久化函数 ====================

def save_result(result: Dict, filepath: str, format: str = 'json') -> None:
    """
    保存处理结果

    Args:
        result: process_long_text的返回结果
        filepath: 保存路径
        format: 保存格式 ('json', 'pickle')
    """
    # 添加时间戳
    result_copy = result.copy()
    result_copy['timestamp'] = datetime.datetime.now().isoformat()

    if format == 'json':
        # 为JSON序列化准备数据（移除不可序列化的对象）
        json_result = result_copy.copy()
        # 简化复杂对象
        if 'expanded_context' in json_result and len(json_result['expanded_context']) > 10000:
            json_result['expanded_context'] = json_result['expanded_context'][:10000] + \
                "...[truncated]"

        with open(filepath, 'w', encoding='utf-8') as f:
            json.dump(json_result, f, indent=2, ensure_ascii=False)
    elif format == 'pickle':
        with open(filepath, 'wb') as f:
            pickle.dump(result_copy, f)
    else:
        raise ValueError("Format must be 'json' or 'pickle'")


def load_result(filepath: str, format: str = 'json') -> Dict:
    """
    加载保存的结果

    Args:
        filepath: 文件路径
        format: 文件格式 ('json', 'pickle')

    Returns:
        加载的结果字典
    """
    if format == 'json':
        with open(filepath, 'r', encoding='utf-8') as f:
            return json.load(f)
    elif format == 'pickle':
        with open(filepath, 'rb') as f:
            return pickle.load(f)
    else:
        raise ValueError("Format must be 'json' or 'pickle'")


def save_processed_document(
    text: str,
    pages: List[List[str]],
    gists: List[str],
    filepath: str
) -> None:
    """
    保存已处理的文档（分页和要点记忆）
    避免重复处理相同文档

    Args:
        text: 原始文本
        pages: 分页结果
        gists: 要点记忆
        filepath: 保存路径
    """
    processed_doc = {
        'original_text': text,
        'pages': pages,
        'gist_memories': gists,
        'text_hash': hash(text),  # 用于验证文档是否变化
        'processed_time': datetime.datetime.now().isoformat(),
        'word_count': count_words(text),
        'page_count': len(pages)
    }

    with open(filepath, 'wb') as f:
        pickle.dump(processed_doc, f)


def load_processed_document(filepath: str) -> Tuple[str, List[List[str]], List[str]]:
    """
    加载已处理的文档

    Args:
        filepath: 文件路径

    Returns:
        (原始文本, 分页结果, 要点记忆)
    """
    with open(filepath, 'rb') as f:
        doc = pickle.load(f)
    return doc['original_text'], doc['pages'], doc['gist_memories']


# ==================== 7. 便利函数和使用示例 ====================

def process_text_file(
    file_path: str,
    query: str,
    **kwargs
) -> Dict:
    """
    直接处理文本文件

    Args:
        file_path: 文本文件路径
        query: 用户查询
        **kwargs: 其他参数传递给process_long_text

    Returns:
        处理结果
    """
    with open(file_path, 'r', encoding='utf-8') as f:
        text = f.read()

    return process_long_text(text, query, **kwargs)


def batch_query(
    text: str,
    queries: List[str],
    save_base_path: str = None,
    **kwargs
) -> List[Dict]:
    """
    对同一文档进行批量查询

    Args:
        text: 文档文本
        queries: 查询列表
        save_base_path: 保存路径基础名
        **kwargs: 其他参数

    Returns:
        结果列表
    """
    results = []
    processed_path = f"{save_base_path}_processed.pkl" if save_base_path else None

    for i, query in enumerate(queries):
        print(f"Processing query {i+1}/{len(queries)}: {query[:50]}...")

        # 第一次查询时保存处理结果，后续查询加载
        if i == 0:
            result = process_long_text(
                text, query,
                save_path=save_base_path,
                **kwargs
            )
        else:
            result = process_long_text(
                text, query,
                load_processed=processed_path,
                **kwargs
            )

        results.append(result)

    return results


if __name__ == "__main__":
    # 使用示例
    print("Read Agent - 使用示例")
    print("=" * 50)

    # 示例文本
    sample_text = """
    Artificial Intelligence (AI) has become one of the most transformative technologies of our time.
    It encompasses various subfields including machine learning, natural language processing,
    computer vision, and robotics. Machine learning, in particular, has seen remarkable advances
    with the development of deep learning algorithms that can process vast amounts of data.

    The applications of AI are vast and growing. In healthcare, AI systems can analyze medical
    images to detect diseases, assist in drug discovery, and personalize treatment plans.
    In transportation, autonomous vehicles use AI to navigate roads safely. In finance,
    AI algorithms detect fraud and enable algorithmic trading.

    However, the rapid advancement of AI also raises important ethical considerations.
    Issues such as bias in AI systems, job displacement due to automation, privacy concerns,
    and the need for transparency in AI decision-making are actively being discussed by
    researchers, policymakers, and society at large.

    Looking forward, the future of AI holds both promise and challenges. Continued research
    in areas like explainable AI, AI safety, and human-AI collaboration will be crucial
    for ensuring that AI benefits humanity while minimizing potential risks.
    """

    # 示例查询
    query = "What are the main applications of AI mentioned in this text?"

    print(f"Sample query: {query}")
    print("\nNote: This example requires a local model API to be running.")
    print("Please ensure your local model server is accessible before running.")

    # 取消注释以下代码来运行示例（需要本地模型API）
    """
    try:
        result = process_long_text(
            text=sample_text,
            query=query,
            verbose=True,
            save_path="example_output"
        )

        print("\nResult:")
        print(format_results(result, include_details=True))

    except Exception as e:
        print(f"Error: {e}")
        print("Please check your local model API configuration.")
    """
