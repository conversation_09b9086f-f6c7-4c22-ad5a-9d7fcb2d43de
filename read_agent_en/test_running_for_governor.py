#!/usr/bin/env python3
"""
Read Agent 测试脚本 - 《竞选州长》
使用马克·吐温的经典短篇小说《竞选州长》测试Read Agent的功能

这个脚本将：
1. 加载《竞选州长》文本
2. 进行多个不同类型的查询测试
3. 展示Read Agent的各种功能
4. 保存测试结果
"""

import sys
import os
import time
from datetime import datetime

# 添加当前目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

try:
    import read_agent
    print("✓ Read Agent 模块导入成功")
except ImportError as e:
    print(f"✗ Read Agent 模块导入失败: {e}")
    sys.exit(1)

def load_running_for_governor():
    """加载《竞选州长》文本"""
    file_path = os.path.join(os.path.dirname(__file__), 'running_for_governer.txt')
    
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            full_text = f.read()
        
        # 提取《竞选州长》部分
        start_marker = "RUNNING FOR GOVERNOR--[Written about 1870.]"
        end_marker = "MARK TWAIN, LP., M.T., B.S., D.T., F.C., and L.E."
        
        start_idx = full_text.find(start_marker)
        end_idx = full_text.find(end_marker) + len(end_marker)
        
        if start_idx == -1 or end_idx == -1:
            print("✗ 无法找到《竞选州长》文本标记")
            return None
        
        story_text = full_text[start_idx:end_idx].strip()
        
        # 获取文本统计
        stats = read_agent.get_text_statistics(story_text)
        print(f"✓ 《竞选州长》文本加载成功")
        print(f"  - 总词数: {stats['total_words']}")
        print(f"  - 总字符数: {stats['total_characters']}")
        print(f"  - 段落数: {stats['paragraphs']}")
        
        return story_text
        
    except FileNotFoundError:
        print(f"✗ 文件未找到: {file_path}")
        return None
    except Exception as e:
        print(f"✗ 加载文件时出错: {e}")
        return None

def test_basic_functionality(text):
    """测试基本功能（不需要API）"""
    print("\n" + "="*60)
    print("测试基本功能（离线测试）")
    print("="*60)
    
    # 测试文本解析
    parsed_text = read_agent.parse_text(text)
    print(f"✓ 文本解析完成，处理后长度: {len(parsed_text)} 字符")
    
    # 测试配置创建
    config = read_agent.create_model_config(
        api_url="http://localhost:1234/v1/chat/completions",
        model="test-model"
    )
    print(f"✓ 模型配置创建: {config}")
    
    # 测试解析函数
    test_responses = [
        "Break point: <57>\nBecause this is a natural transition.",
        "I want to review pages [1, 3, 5, 7] for more details.",
        "The relevant pages are [2, 4] based on the query."
    ]
    
    for i, response in enumerate(test_responses):
        if "Break point" in response:
            result = read_agent.parse_pause_point(response)
            print(f"✓ 暂停点解析测试 {i+1}: {result}")
        else:
            result = read_agent.parse_page_ids(response)
            print(f"✓ 页面ID解析测试 {i+1}: {result}")

def create_test_queries():
    """创建测试查询列表"""
    return [
        {
            "query": "What is the main plot of this story?",
            "description": "情节概述查询"
        },
        {
            "query": "What accusations were made against Mark Twain during his campaign?",
            "description": "具体细节查询"
        },
        {
            "query": "How does Mark Twain use humor and satire in this story?",
            "description": "文学分析查询"
        },
        {
            "query": "What is the moral or message of this story?",
            "description": "主题思想查询"
        },
        {
            "query": "Describe the character of Mark Twain as portrayed in this story.",
            "description": "人物分析查询"
        }
    ]

def test_with_api(text, api_config):
    """使用API进行完整测试"""
    print("\n" + "="*60)
    print("完整功能测试（需要API）")
    print("="*60)
    
    queries = create_test_queries()
    results = []
    
    print(f"将进行 {len(queries)} 个查询测试...")
    print(f"API配置: {api_config['api_url']}")
    
    for i, test_case in enumerate(queries, 1):
        print(f"\n--- 测试 {i}/{len(queries)}: {test_case['description']} ---")
        print(f"查询: {test_case['query']}")
        
        try:
            start_time = time.time()
            
            # 第一次查询保存处理结果
            if i == 1:
                result = read_agent.process_long_text(
                    text=text,
                    query=test_case['query'],
                    save_path=f"test_result_{i}",
                    verbose=True,
                    **api_config
                )
            else:
                # 后续查询使用已处理的文档
                result = read_agent.process_long_text(
                    text=text,
                    query=test_case['query'],
                    load_processed="test_result_1_processed.pkl",
                    save_path=f"test_result_{i}",
                    verbose=True,
                    **api_config
                )
            
            end_time = time.time()
            processing_time = end_time - start_time
            
            print(f"✓ 处理完成，耗时: {processing_time:.2f}秒")
            print(f"✓ 压缩率: {result['compression_rate']:.2%}")
            print(f"✓ 使用页面: {result['relevant_pages']}")
            print(f"✓ 总页面数: {len(result['pages'])}")
            
            # 显示响应的前200字符
            response_preview = result['response'][:200] + "..." if len(result['response']) > 200 else result['response']
            print(f"✓ 响应预览: {response_preview}")
            
            results.append({
                'test_case': test_case,
                'result': result,
                'processing_time': processing_time
            })
            
        except Exception as e:
            print(f"✗ 测试失败: {e}")
            results.append({
                'test_case': test_case,
                'error': str(e),
                'processing_time': None
            })
    
    return results

def generate_test_report(results, text_stats):
    """生成测试报告"""
    print("\n" + "="*60)
    print("测试报告")
    print("="*60)
    
    print(f"测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"测试文档: 《竞选州长》by 马克·吐温")
    print(f"文档统计: {text_stats['total_words']} 词, {text_stats['paragraphs']} 段落")
    
    successful_tests = [r for r in results if 'error' not in r]
    failed_tests = [r for r in results if 'error' in r]
    
    print(f"\n测试结果:")
    print(f"  ✓ 成功: {len(successful_tests)}")
    print(f"  ✗ 失败: {len(failed_tests)}")
    
    if successful_tests:
        avg_time = sum(r['processing_time'] for r in successful_tests) / len(successful_tests)
        avg_compression = sum(r['result']['compression_rate'] for r in successful_tests) / len(successful_tests)
        
        print(f"\n性能统计:")
        print(f"  平均处理时间: {avg_time:.2f}秒")
        print(f"  平均压缩率: {avg_compression:.2%}")
        
        print(f"\n详细结果:")
        for i, result in enumerate(successful_tests, 1):
            test_case = result['test_case']
            res = result['result']
            print(f"\n  {i}. {test_case['description']}")
            print(f"     查询: {test_case['query']}")
            print(f"     压缩率: {res['compression_rate']:.2%}")
            print(f"     使用页面: {res['relevant_pages']}")
            print(f"     处理时间: {result['processing_time']:.2f}秒")
    
    if failed_tests:
        print(f"\n失败的测试:")
        for i, result in enumerate(failed_tests, 1):
            test_case = result['test_case']
            print(f"  {i}. {test_case['description']}: {result['error']}")

def main():
    """主测试函数"""
    print("Read Agent 测试 - 《竞选州长》")
    print("="*60)
    
    # 加载文本
    text = load_running_for_governor()
    if not text:
        print("无法加载测试文本，退出测试")
        return
    
    # 基本功能测试
    test_basic_functionality(text)
    
    # 询问是否进行API测试
    print("\n" + "="*60)
    print("API测试配置")
    print("="*60)
    
    # 默认API配置
    default_config = {
        'api_url': 'http://************:1234/v1/chat/completions',
        'model': 'qwen3-30b-a3b',
        'api_key': 'lm_studio'
    }
    
    print("默认API配置:")
    for key, value in default_config.items():
        print(f"  {key}: {value}")
    
    # 注意：在实际使用中，您可能需要修改这些配置
    print("\n注意：请确保您的本地模型API正在运行")
    print("如果需要修改配置，请编辑脚本中的 default_config")
    
    try:
        # 进行API测试
        results = test_with_api(text, default_config)
        
        # 生成报告
        text_stats = read_agent.get_text_statistics(text)
        generate_test_report(results, text_stats)
        
        print(f"\n测试完成！结果已保存到当前目录的 test_result_*.json 文件中")
        
    except KeyboardInterrupt:
        print("\n测试被用户中断")
    except Exception as e:
        print(f"\n测试过程中出现错误: {e}")
        print("请检查API配置和网络连接")

if __name__ == "__main__":
    main()
