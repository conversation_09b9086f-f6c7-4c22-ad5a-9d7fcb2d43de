# 项目需求文档：Read Agent 2.0

## 1. 项目愿景与目标

本项目旨在将现有的 `read_agent.py` 单体脚本，重构并扩展为一个模块化、可扩展、高性能的检索增强生成（RAG）系统。该系统将以健壮的数据库（SQLite + ChromaDB）为核心，支持对本地书籍（尤其是EPUB格式）进行自动化、增量的处理和深度查询，并提供灵活的、可插拔的组件来适应未来的功能扩展。

最终目标是打造一个能持续学习、高效检索、智能交互的个人知识库引擎。

---

## 2. 核心功能需求

### 一、 书籍处理与索引 (Book Processing & Indexing)

此部分定义了系统如何从原始文件（如EPUB）中提取信息，并将其转化为可检索的知识。

#### 1.1. 自动化书籍监控
- **需求**: 系统应能监控一个或多个指定文件夹（如 `epubs/`），自动发现新增、修改或删除的书籍。
- **实现要点**:
    - 可以是一个后台服务或一个在主程序启动时触发的扫描任务。
    - 通过对比文件名、文件修改时间或文件内容的哈希值来判断文件状态。
    - 发现变更后，自动触发后续的索引流程。

#### 1.2. 结构化内容解析
- **需求**: 系统需要能深度解析文档，特别是EPUB格式。
- **实现要点**:
    - 从EPUB文件中抽取出核心的元数据，包括但不限于：`书名 (Title)`、`作者 (Creator)`、`语言 (Language)`、`出版社 (Publisher)`。
    - 解析并存储完整的目录结构（TOC），保留章节的层级关系和原始链接。
    - 解析并存储书籍的线性阅读顺序（Spine）。
    - 将解析出的元信息、TOC、Spine以及每个章节的原始内容存入 **SQLite** 数据库，建立清晰的关联。

#### 1.3. 分层内容处理
- **需求**: 对解析出的章节原文进行多层次、可追踪的处理。
- **实现要点**:
    - **分页 (Pagination)**: 将每个章节的文本内容，按照既定算法（如 `read_agent` v1.0 的智能分页）切分成更小的、语义连贯的页面（Page）。分页结果存入SQLite。
    - **处理 (Processing)**: 对每个页面执行一个或多个处理操作。这些操作以“插件”形式存在。
    - **状态追踪**: SQLite需要记录每本书籍、每个章节、每个页面的处理状态（如：`已解析`、`已分页`、`已压缩`、`已向量化`），确保流程中断后可以恢复。

#### 1.4. 向量化与索引
- **需求**: 为处理过的内容生成向量表示，以便进行语义检索。
- **实现要点**:
    - 当一个页面的处理（如压缩）完成后，触发向量化流程。
    - 系统应能对页面的**摘要**或**原文**（或两者结合）进行向量化。
    - 生成的向量及与之对应的页面ID（作为映射）存入 **ChromaDB**。

#### 1.5. 可插拔处理模块 (Processor Plugins)
- **需求**: 摘要/压缩或其他由提示词驱动的处理操作应该是“插件式”的。
- **实现要点**:
    - 定义一个标准的插件接口（如一个接受文本、返回处理后文本的Python类或函数）。
    - 系统可以加载一个或多个处理插件，并按顺序链式执行。
    - 除了“摘要”，插件的例子还可以包括：`关键词提取`、`命名实体识别`、`生成设想问题`等。
    - 每个插件的处理结果都应能被存储和向量化，以增强书籍的描述能力和可检索维度。

---

### 二、 查询与交互 (Query & Interaction)

此部分定义了用户如何与系统交互，以及系统如何利用索引数据生成回答。

#### 2.1. 可插拔的查询重写 (Query Rewriting Plugins)
- **需求**: 用户输入的原始查询在执行检索前，应经过一个或多个“查询改写”插件的处理。
- **实现要点**:
    - 定义查询改写插件的接口。
    - 插件可以链式调用，逐步优化查询。
    - **插件示例**:
        - **同义词扩展**: 增加查询的同义词，提高覆盖面。
        - **HyDE (Hypothetical Document Embeddings)**: 让LLM基于查询生成一个假想的、最能回答该问题的书籍片段，然后对这个片段进行向量化以用于检索。
        - **意图识别**: 判断用户是想“查询”、“搜索文件”还是执行“删除”等操作，并将查询分发到不同模块。

#### 2.2. 混合检索与上下文构建
- **需求**: 使用改写后的查询，高效、准确地从数据库中召回最相关的信息，并构建用于生成最终答案的上下文。
- **实现要点**:
    - **向量化**: 对最终的查询文本进行向量化。
    - **召回**: 使用查询向量从 ChromaDB 中召回 top-k 个最相关的页面ID。
    - **（可选）重排序 (Re-ranking)**: 对召回的结果进行更精细的排序，以提高最终上下文的质量。
    - **上下文构建**: 根据排序后的页面ID，从 SQLite 中获取页面原文或摘要，并按照预设的模板（如 `read_agent` v1.0 的上下文构建方式）拼接成最终的上下文。

#### 2.3. 模块化响应生成
- **需求**: 最终的响应生成过程也应是模块化和插件化的。
- **实现要点**:
    - 根据查询意图或预设模式，可以选择不同的“响应生成插件”。
    - **插件示例**:
        - **标准问答插件**: 基于上下文，直接回答用户问题。
        - **总结插件**: 对检索到的内容进行总结。
        - **结构化数据提取插件**: 从上下文中提取特定格式的信息（如JSON）。

#### 2.4. 多轮对话管理
- **需求**: 系统需要具备管理多轮对话状态的能力。
- **实现要点**:
    - 在SQLite中建立会话（Session）和对话历史（History）表。
    - 存储每一轮的用户查询、系统使用的上下文、最终生成的回答。
    - 在处理后续查询时，能够将之前的对话历史作为背景信息，以正确理解指代（如“它怎么样了？”）和进行追问。

---

## 3. 架构设计与模块划分

为了实现上述需求并保证系统的可维护性和扩展性，项目将采用模块化的分层架构。

### 3.1. 拟定文件结构

```
/dev/
├── main.py                 # 应用主入口和编排层
├── config.py               # 全局配置中心
│
├── hooks/                  # 存放插件系统的核心契约
│   ├── __init__.py
│   ├── hookspecs.py        # 定义所有Pluggy的Hook-Specification
│   └── plugin_manager.py   # 封装PluginManager的初始化和插件加载
│
├── corpus/                 # 存放待处理的原始书籍 (如 .epub, .pdf)
│
├── book_parsers/       # 负责解析不同格式的原始书籍
│   ├── __init__.py
│   ├── cfi_generator.py    # 专门的CFI生成逻辑
│   └── epub_parser.py      # EPUB文件解析器 (将调用cfi_generator)
│
├── processing/             # 负责核心的文本处理算法
│   ├── __init__.py
│   ├── core_processor.py   # 实现分页、调用插件处理等
│   └── text_utils.py       # 语言检测、文本计数等通用工具
│
├── storage/                # 负责所有书籍数据的持久化和检索
│   ├── __init__.py
│   └── storage_manager.py  # 封装SQLite和ChromaDB的交互逻辑
│
├── llm/                    # 负责与大语言模型相关的一切交互
│   ├── __init__.py
│   ├── llm_interface.py    # 封装生成式LLM的API调用
│   ├── embedding.py        # 封装嵌入模型的API调用
│   └── prompt_manager.py   # 负责从插件系统加载和管理提示词
│
├── query/                  # 负责处理用户查询
│   ├── __init__.py
│   └── query_manager.py    # 实现查询改写、检索、上下文构建等
│
├── system_plugins/         # 存放系统内置的、默认的插件实现
│   ├── __init__.py
│   └── default_prompts.py  # 默认提示词插件
│
├── user_plugins/           # 存放用户自定义的、可插拔的插件
│   ├── __init__.py
│   ├── processors/         # (示例) 文档处理插件
│   └── query_rewriters/    # (示例) 查询改写插件
│
└── data/                     # 存放数据库文件和ChromaDB的持久化数据
    ├── documents.db
    └── chroma_data/
```

### 3.2. 模块功能描述

- **`main.py`**: **应用编排器**。负责解析命令行参数，调用其他模块，按顺序执行“书籍监控 -> 解析 -> 处理 -> 查询 -> 响应”的完整流程。
- **`config.py`**: **全局配置中心**。存放数据库路径、LLM API密钥、模型名称、处理参数等所有可配置项。
- **`hooks/`**: **插件契约与管理层**。定义系统的所有扩展点（`hookspecs.py`）并管理插件的加载与调度（`plugin_manager.py`），是插件化架构的核心。
- **`corpus/`**: **原始书籍库**。存放所有待处理的源文件，如 `.epub`, `.pdf`, `.txt` 等。
- **`document_parsers/`**: **书籍解析层**。每个`_parser.py`文件负责一种文档类型，提供一个统一的`parse()`接口。`epub_parser.py`将内置`cfi_generator.py`的逻辑，输出包含CFI的结构化数据。
- **`processing/`**: **核心处理层**。`core_processor.py`是书籍处理的核心，负责文本的分页和调用`plugins/processors`中的插件对文本进行加工。
- **`storage/`**: **数据持久化层**。`storage_manager.py`是唯一与数据库直接交互的模块，为上层应用提供清晰的、面向业务的接口（如`add_book`, `query_vectors`），屏蔽底层数据库（SQLite, ChromaDB）的实现细节。
- **`llm/`**: **语言模型接口层**。将所有对外部LLM的API调用（包括生成式和嵌入式）封装在此。`prompt_manager.py`负责通过插件系统加载和提供提示词。
- **`query/`**: **查询处理层**。`query_manager.py`负责完整的RAG检索流程，包括调用查询改写插件、向量化查询、向`storage`模块请求数据、构建最终上下文等。
- **`system_plugins/`**: **系统内置插件目录**。提供系统运行所必需的默认插件实现，如默认的提示词。
- **`user_plugins/`**: **用户自定义插件目录**。用户或开发者可在此目录下添加自己的插件来扩展系统功能，系统会自动发现并加载。
- **`data/`**: **数据存储目录**。用于存放SQLite数据库文件和ChromaDB的持久化数据，应被`.gitignore`忽略。

---

## 4. 插件系统设计 (`Pluggy`驱动)

为了实现模块化和可扩展性，系统的核心交互将由 `Pluggy` 框架驱动。我们将定义一系列“钩子”（Hooks）作为扩展点，系统的不同模块和外部插件可以通过实现这些钩子来注入功能。

### 4.1. 核心理念
- **钩子（Hook）即接口**：我们不定义抽象基类，而是定义钩子规范。一个模块或插件的功能是通过实现一个或多个钩子来暴露的。
- **集中管理**：一个统一的 `PluginManager` 实例（在`hooks/plugin_manager.py`中实现）负责在系统启动时发现、注册和管理所有插件。
- **数据对象传递**：模块和插件之间通过标准化的数据类（如 `dataclasses`）进行通信，而不是依赖零散的变量。

### 4.2. 初始钩子定义 (Hook Specifications)

我们将在 `hooks/hookspecs.py` 中定义所有钩子规范。以下是第一批需要定义的钩子：

1.  **`register_prompts() -> dict`**
    - **目的**: 允许插件向系统注册提示词模板。
    - **实现**: 插件返回一个字典，键为提示词名称，值为模板字符串。
    - **应用**: `llm/prompt_manager.py` 在启动时调用此钩子，收集所有模板并整合成一个最终的集合。

2.  **`process_page(page: PageObject) -> PageObject`**
    - **目的**: 对分页后的页面内容进行链式处理。
    - **实现**: 插件接收一个 `PageObject` 对象，对其内容进行处理（如摘要、提取关键词），并将结果附加到该对象上后返回。
    - **应用**: `processing/core_processor.py` 在处理每个页面时，会依次调用所有实现了此钩子的插件。

3.  **`rewrite_query(context: QueryContext) -> QueryContext`**
    - **目的**: 在执行检索前，对用户的查询进行改写和优化。
    - **实现**: 插件接收包含原始查询和对话历史的 `QueryContext` 对象，修改其查询属性后返回。
    - **应用**: `query/query_manager.py` 在开始检索流程前，调用此钩子。

### 4.3. 示例：提示词插件化流程

1.  **定义规范 (Spec)**: 在 `hooks/hookspecs.py` 中定义 `register_prompts` 钩子。
2.  **默认实现 (Default Plugin)**: 在 `system_plugins/default_prompts.py` 中，创建一个 `register_prompts` 的实现，返回所有基础的提示词模板。
3.  **扩展实现 (External Plugin)**: 用户可以在 `user_plugins/` 目录下创建新文件，同样实现 `register_prompts` 钩子，返回新的或需要覆盖的提示词。
4.  **系统整合**: `llm/prompt_manager.py` 通过 `hooks/plugin_manager.py` 调用钩子，并根据注册顺序（后注册的优先）合并结果，生成最终的提示词集合供系统使用。

通过这种方式，系统的几乎所有关键行为——从数据处理到查询优化——都可以被灵活地扩展和定制，而无需修改核心代码。

---

## 5. 核心技术选型与实现策略

### 5.1. EPUB CFI (Canonical Fragment Identifier) 生成

- **目标**: 实现文档内容与原文的精确映射，支持前端高亮和跳转。
- **挑战**: 缺少一个能同时解析EPUB包并生成CFI的、功能完备的纯Python库。
- **决策**: 采用**以DOM遍历为核心的、与内容提取同步进行的策略**。
- **实现路径**:
    1.  **统一处理入口**: CFI的生成和文本内容的提取，将在 `document_parsers/epub_parser.py` 模块中统一完成，避免信息丢失和重复遍历。
    2.  **DOM驱动流程**: 解析器将不再使用 `get_text()` 提取扁平化文本。而是采用递归函数遍历章节的XHTML DOM树。
    3.  **同步生成**: 在遍历每个DOM元素（如一个`<p>`标签）时，**同步执行**以下操作：
        - **计算CFI**: 根据当前元素在DOM树中的层级和同级顺序，为其计算出精确的元素级CFI。
        - **提取文本**: 仅提取当前元素下的文本内容。
        - **（可选）分割句子**: 对提取出的文本块，可立即调用文本分割工具，得到句子列表。
    4.  **结构化输出**: `epub_parser.py` 的输出将是一个结构化的数据列表，其中每个对象都同时包含**内容**和**位置**信息。例如：
        ```python
        [
            {'text': '这是第一段。', 'cfi': 'epubcfi(/6/10!/4/2)'},
            {'text': '这是第二段。', 'cfi': 'epubcfi(/6/10!/4/4)'}
        ]
        ```
    5.  **数据库存储**: `storage_manager.py` 在设计 `pages` 表时，将包含 `start_cfi` 和 `end_cfi` 字段，用于存储由 `core_processor.py` 根据上述结构化数据组合成的页面所对应的CFI范围。
- **优势**: 此方案逻辑清晰，确保了CFI的生成精度，并在一次DOM遍历中完成了多项任务，兼顾了准确性和效率。

### 5.2. 数据库模式演进
- **问题**: 随着功能迭代，数据库表结构可能需要变更。
- **策略**: 引入一个轻量级的数据库迁移机制。在 `storage_manager.py` 中，将包含一个版本号管理或一个简单的迁移脚本执行器。系统启动时会检查数据库版本，并自动应用必要的更新，以避免要求用户删除数据库并重新索引。
