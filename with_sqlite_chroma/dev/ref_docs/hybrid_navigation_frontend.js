// frontend/navigation.js
class HybridNavigationSystem {
    constructor(rendition, bookManager) {
        this.rendition = rendition;
        this.bookManager = bookManager;
        this.currentBook = null;
        this.navigationHistory = [];
        this.retryAttempts = 3;
    }

    /**
     * 主要的导航方法 - 处理后端推荐
     */
    async navigateToRecommendation(recommendation) {
        console.log('Navigating to recommendation:', recommendation);
        
        try {
            // 1. 验证推荐数据
            if (!this.validateRecommendation(recommendation)) {
                throw new Error('Invalid recommendation data');
            }

            // 2. 加载目标书籍（如果需要）
            await this.ensureBookLoaded(recommendation.book_id);

            // 3. 执行混合定位策略
            const success = await this.executeNavigationStrategies(recommendation);

            if (success) {
                // 4. 成功后的处理
                this.onNavigationSuccess(recommendation);
                return true;
            } else {
                // 5. 失败后的降级处理
                return await this.fallbackNavigation(recommendation);
            }

        } catch (error) {
            console.error('Navigation failed:', error);
            this.onNavigationError(error, recommendation);
            return false;
        }
    }

    /**
     * 验证推荐数据的完整性
     */
    validateRecommendation(recommendation) {
        return recommendation && 
               recommendation.book_id && 
               recommendation.location_info &&
               recommendation.location_info.spine_index !== undefined;
    }

    /**
     * 确保目标书籍已加载
     */
    async ensureBookLoaded(bookId) {
        if (this.currentBook?.id === bookId) {
            return; // 已经是当前书籍
        }

        // 显示加载状态
        this.showLoadingState(`Loading book: ${bookId}`);

        try {
            // 通过书籍管理器加载书籍
            this.currentBook = await this.bookManager.loadBook(bookId);
            
            // 等待epub.js完全加载
            await this.currentBook.ready;
            
            // 更新渲染器
            await this.rendition.display();
            
            console.log(`Successfully loaded book: ${bookId}`);
            
        } catch (error) {
            console.error(`Failed to load book ${bookId}:`, error);
            throw new Error(`Book loading failed: ${error.message}`);
        } finally {
            this.hideLoadingState();
        }
    }

    /**
     * 执行混合定位策略
     */
    async executeNavigationStrategies(recommendation) {
        const { location_info } = recommendation;
        
        // 定义策略数组，按优先级排序
        const strategies = [
            {
                name: 'exact_match',
                method: () => this.navigateByExactMatch(location_info),
                condition: location_info.method === 'exact_match'
            },
            {
                name: 'spine_and_text',
                method: () => this.navigateBySpineAndText(location_info),
                condition: location_info.spine_index !== undefined
            },
            {
                name: 'element_hints',
                method: () => this.navigateByElementHints(location_info),
                condition: location_info.element_hints?.id || location_info.element_hints?.class
            },
            {
                name: 'text_search',
                method: () => this.navigateByTextSearch(location_info),
                condition: location_info.search_text
            },
            {
                name: 'fuzzy_search',
                method: () => this.navigateByFuzzySearch(location_info),
                condition: location_info.search_text
            }
        ];

        // 根据后端推荐的方法优先选择策略
        const prioritizedStrategies = this.prioritizeStrategies(strategies, location_info.method);

        // 依次尝试每种策略
        for (const strategy of prioritizedStrategies) {
            if (!strategy.condition) continue;

            console.log(`Trying navigation strategy: ${strategy.name}`);
            
            try {
                const result = await this.executeWithRetry(
                    strategy.method, 
                    this.retryAttempts,
                    strategy.name
                );
                
                if (result) {
                    console.log(`Navigation successful with strategy: ${strategy.name}`);
                    return true;
                }
            } catch (error) {
                console.warn(`Strategy ${strategy.name} failed:`, error);
                continue;
            }
        }

        return false; // 所有策略都失败了
    }

    /**
     * 根据后端推荐方法调整策略优先级
     */
    prioritizeStrategies(strategies, recommendedMethod) {
        const prioritized = [...strategies];
        
        // 将推荐的方法移到前面
        const recommendedIndex = prioritized.findIndex(s => s.name === recommendedMethod);
        if (recommendedIndex > 0) {
            const recommended = prioritized.splice(recommendedIndex, 1)[0];
            prioritized.unshift(recommended);
        }
        
        return prioritized;
    }

    /**
     * 带重试的执行方法
     */
    async executeWithRetry(method, maxAttempts, strategyName) {
        for (let attempt = 1; attempt <= maxAttempts; attempt++) {
            try {
                const result = await method();
                if (result) return result;
                
                // 如果不是最后一次尝试，等待一下再重试
                if (attempt < maxAttempts) {
                    await this.delay(500 * attempt); // 递增延迟
                }
            } catch (error) {
                if (attempt === maxAttempts) throw error;
                console.warn(`${strategyName} attempt ${attempt} failed, retrying...`);
                await this.delay(500 * attempt);
            }
        }
        return false;
    }

    /**
     * 策略1: 精确匹配导航
     */
    async navigateByExactMatch(locationInfo) {
        const { spine_index, search_text } = locationInfo;
        
        // 直接跳转到章节
        await this.rendition.display(spine_index);
        await this.waitForRender();
        
        // 在当前页面精确搜索
        const cfi = await this.findTextInCurrentView(search_text, true);
        
        if (cfi) {
            await this.rendition.display(cfi);
            this.highlightText(search_text);
            return true;
        }
        
        return false;
    }

    /**
     * 策略2: 章节索引 + 文本搜索
     */
    async navigateBySpineAndText(locationInfo) {
        const { spine_index, search_text, chapter_href } = locationInfo;
        
        try {
            // 跳转到指定章节
            if (chapter_href) {
                await this.rendition.display(chapter_href);
            } else {
                await this.rendition.display(spine_index);
            }
            
            await this.waitForRender();
            
            // 在当前章节搜索文本
            const found = await this.searchInCurrentChapter(search_text);
            
            if (found) {
                this.highlightText(search_text);
                return true;
            }
            
        } catch (error) {
            console.error('Spine navigation failed:', error);
        }
        
        return false;
    }

    /**
     * 策略3: 使用元素提示导航
     */
    async navigateByElementHints(locationInfo) {
        const { spine_index, element_hints, search_text } = locationInfo;
        
        // 先跳转到章节
        await this.rendition.display(spine_index);
        await this.waitForRender();
        
        // 尝试通过ID查找元素
        if (element_hints.id) {
            const element = this.rendition.getContents().document.getElementById(element_hints.id);
            if (element) {
                this.scrollToElement(element);
                this.highlightElement(element);
                return true;
            }
        }
        
        // 尝试通过class查找元素
        if (element_hints.class) {
            const elements = this.rendition.getContents().document.getElementsByClassName(element_hints.class);
            for (const element of elements) {
                if (element.textContent.includes(search_text)) {
                    this.scrollToElement(element);
                    this.highlightElement(element);
                    return true;
                }
            }
        }
        
        return false;
    }

    /**
     * 策略4: 全文搜索
     */
    async navigateByTextSearch(locationInfo) {
        const { search_text } = locationInfo;
        
        return new Promise((resolve) => {
            // 使用epub.js的搜索功能
            this.currentBook.spine.each((section) => {
                return section.load(this.currentBook.load.bind(this.currentBook))
                    .then((doc) => {
                        const content = doc.documentElement.textContent || doc.documentElement.innerText;
                        const index = content.toLowerCase().indexOf(search_text.toLowerCase());
                        
                        if (index !== -1) {
                            // 找到了，生成CFI并跳转
                            const range = this.createRangeFromIndex(doc, index, search_text.length);
                            if (range) {
                                const cfi = section.cfiFromRange(range);
                                this.rendition.display(cfi).then(() => {
                                    this.highlightText(search_text);
                                    resolve(true);
                                });
                                return true; // 停止搜索
                            }
                        }
                        return false;
                    })
                    .catch((error) => {
                        console.warn('Search section failed:', error);
                        return false;
                    });
            }).then(() => {
                resolve(false); // 搜索完成但未找到
            });
        });
    }

    /**
     * 策略5: 模糊搜索
     */
    async navigateByFuzzySearch(locationInfo) {
        const { search_text, context_before, context_after } = locationInfo;
        
        // 使用关键词进行模糊搜索
        const keywords = this.extractKeywords(search_text);
        
        for (const keyword of keywords) {
            const found = await this.navigateByTextSearch({ search_text: keyword });
            if (found) {
                // 验证上下文是否匹配
                if (this.validateContext(context_before, context_after)) {
                    return true;
                }
            }
        }
        
        return false;
    }

    /**
     * 降级导航 - 当所有策略都失败时
     */
    async fallbackNavigation(recommendation) {
        console.log('Using fallback navigation');
        
        const { location_info } = recommendation;
        
        try {
            // 至少跳转到章节级别
            if (location_info.spine_index !== undefined) {
                await this.rendition.display(location_info.spine_index);
                
                // 显示提示信息
                this.showNavigationHint(
                    `Navigated to chapter. Please look for: "${location_info.search_text}"`
                );
                
                return true;
            }
            
            // 如果连章节都无法定位，跳转到书籍开始
            await this.rendition.display(0);
            this.showNavigationHint(
                `Could not locate exact position. Showing book beginning. Target: "${location_info.search_text}"`
            );
            
            return true;
            
        } catch (error) {
            console.error('Fallback navigation failed:', error);
            return false;
        }
    }

    // 辅助方法
    async waitForRender(timeout = 2000) {
        return new Promise((resolve) => {
            const checkRendered = () => {
                if (this.rendition.manager && this.rendition.manager.isRendered()) {
                    resolve();
                } else {
                    setTimeout(checkRendered, 100);
                }
            };
            
            checkRendered();
            
            // 超时保护
            setTimeout(resolve, timeout);
        });
    }

    async delay(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }

    async findTextInCurrentView(searchText, exact = false) {
        const contents = this.rendition.getContents();
        if (!contents || !contents.document) return null;
        
        const walker = document.createTreeWalker(
            contents.document.body,
            NodeFilter.SHOW_TEXT,
            null,
            false
        );
        
        let node;
        while (node = walker.nextNode()) {
            const text = node.textContent;
            const index = exact ? 
                text.indexOf(searchText) : 
                text.toLowerCase().indexOf(searchText.toLowerCase());
                
            if (index !== -1) {
                const range = contents.document.createRange();
                range.setStart(node, index);
                range.setEnd(node, index + searchText.length);
                
                return contents.cfiFromRange(range);
            }
        }
        
        return null;
    }

    async searchInCurrentChapter(searchText) {
        const contents = this.rendition.getContents();
        if (!contents) return false;
        
        const bodyText = contents.document.body.textContent || contents.document.body.innerText;
        return bodyText.toLowerCase().includes(searchText.toLowerCase());
    }

    createRangeFromIndex(doc, index, length) {
        const walker = document.createTreeWalker(
            doc.body,
            NodeFilter.SHOW_TEXT,
            null,
            false
        );
        
        let currentIndex = 0;
        let node;
        
        while (node = walker.nextNode()) {
            const nodeLength = node.textContent.length;
            
            if (currentIndex + nodeLength > index) {