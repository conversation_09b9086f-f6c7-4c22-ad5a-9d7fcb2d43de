# dev/storage/storage_manager.py
import sqlite3
import json
import os
import uuid
from typing import List, Dict, Optional

import chromadb

from ..config import (
    DATA_DIR, SQLITE_DB_NAME, 
    CHROMA_COLLECTION_NAME, CHROMA_SUMMARY_COLLECTION_NAME, CHROMA_SENTENCE_COLLECTION_NAME
)

class StorageManager:
    def __init__(self, rebuild_db: bool = False):
        os.makedirs(DATA_DIR, exist_ok=True)
        db_path = os.path.join(DATA_DIR, SQLITE_DB_NAME)
        
        if rebuild_db and os.path.exists(db_path):
            chroma_path = os.path.join(DATA_DIR, "chroma_db")
            if os.path.exists(chroma_path):
                import shutil
                shutil.rmtree(chroma_path)
            os.remove(db_path)

        self.conn = sqlite3.connect(db_path)
        self.conn.row_factory = self._dict_factory
        self.cursor = self.conn.cursor()
        
        if rebuild_db:
            self.setup_databases()

        chroma_path = os.path.join(DATA_DIR, "chroma_db")
        self.chroma_client = chromadb.PersistentClient(path=chroma_path)
        self.page_collection = self.chroma_client.get_or_create_collection(name=CHROMA_COLLECTION_NAME)
        self.summary_collection = self.chroma_client.get_or_create_collection(name=CHROMA_SUMMARY_COLLECTION_NAME)
        self.sentence_collection = self.chroma_client.get_or_create_collection(name=CHROMA_SENTENCE_COLLECTION_NAME)

    @staticmethod
    def _dict_factory(cursor, row):
        d = {}
        for idx, col in enumerate(cursor.description):
            d[col[0]] = row[idx]
        return d

    def setup_databases(self):
        self.cursor.execute("""
        CREATE TABLE IF NOT EXISTS books (
            id TEXT PRIMARY KEY,
            source_path TEXT NOT NULL UNIQUE,
            content_hash TEXT NOT NULL,
            detected_language TEXT,
            metadata TEXT,
            status TEXT NOT NULL DEFAULT 'new',
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
        """)
        self.cursor.execute("""
        CREATE TABLE IF NOT EXISTS chapters (
            id TEXT PRIMARY KEY,
            book_id TEXT NOT NULL,
            chapter_index INTEGER NOT NULL,
            title TEXT,
            content TEXT,
            spine_index INTEGER,
            chapter_href TEXT,
            FOREIGN KEY (book_id) REFERENCES books (id)
        )
        """)
        self.cursor.execute("""
        CREATE TABLE IF NOT EXISTS sentences (
            id TEXT PRIMARY KEY,
            chapter_id TEXT NOT NULL,
            sentence_index_in_chapter INTEGER NOT NULL,
            content TEXT NOT NULL,
            text_hash TEXT,
            element_hints TEXT,
            FOREIGN KEY (chapter_id) REFERENCES chapters (id)
        )
        """)
        self.cursor.execute("""
        CREATE TABLE IF NOT EXISTS pages (
            id TEXT PRIMARY KEY,
            chapter_id TEXT NOT NULL,
            page_index_in_chapter INTEGER NOT NULL,
            content TEXT,
            start_sentence_id TEXT,
            end_sentence_id TEXT,
            FOREIGN KEY (chapter_id) REFERENCES chapters (id),
            FOREIGN KEY (start_sentence_id) REFERENCES sentences (id),
            FOREIGN KEY (end_sentence_id) REFERENCES sentences (id)
        )
        """)
        self.cursor.execute("""
        CREATE TABLE IF NOT EXISTS processed_data (
            id TEXT PRIMARY KEY,
            page_id TEXT NOT NULL,
            processor_name TEXT NOT NULL,
            data TEXT,
            FOREIGN KEY (page_id) REFERENCES pages (id)
        )
        """)
        self.conn.commit()

    def add_book(self, source_path: str, content_hash: str, detected_language: str, metadata: dict) -> str:
        book_uuid = str(uuid.uuid4())
        self.cursor.execute(
            "INSERT INTO books (id, source_path, content_hash, detected_language, metadata) VALUES (?, ?, ?, ?, ?)",
            (book_uuid, source_path, content_hash, detected_language, json.dumps(metadata, ensure_ascii=False))
        )
        self.conn.commit()
        return book_uuid

    def add_chapter_only(self, book_id: str, chapter_index: int, title: str, content: str, spine_index: int, chapter_href: str) -> str:
        chapter_uuid = str(uuid.uuid4())
        self.cursor.execute(
            "INSERT INTO chapters (id, book_id, chapter_index, title, content, spine_index, chapter_href) VALUES (?, ?, ?, ?, ?, ?, ?)",
            (chapter_uuid, book_id, chapter_index, title, content, spine_index, chapter_href)
        )
        self.conn.commit()
        return chapter_uuid

    def add_sentence_only(self, chapter_id: str, sentence_index_in_chapter: int, content: str, text_hash: str, element_hints: dict) -> str:
        sentence_uuid = str(uuid.uuid4())
        self.cursor.execute(
            "INSERT INTO sentences (id, chapter_id, sentence_index_in_chapter, content, text_hash, element_hints) VALUES (?, ?, ?, ?, ?, ?)",
            (sentence_uuid, chapter_id, sentence_index_in_chapter, content, text_hash, json.dumps(element_hints, ensure_ascii=False))
        )
        self.conn.commit()
        return sentence_uuid

    def add_page_only(self, chapter_id: str, page_index_in_chapter: int, content: str, start_sentence_id: str, end_sentence_id: str) -> str:
        page_uuid = str(uuid.uuid4())
        self.cursor.execute(
            "INSERT INTO pages (id, chapter_id, page_index_in_chapter, content, start_sentence_id, end_sentence_id) VALUES (?, ?, ?, ?, ?, ?)",
            (page_uuid, chapter_id, page_index_in_chapter, content, start_sentence_id, end_sentence_id)
        )
        self.conn.commit()
        return page_uuid

    def add_processed_data(self, page_id: str, processor_name: str, data: str) -> str:
        processed_data_uuid = str(uuid.uuid4())
        self.cursor.execute(
            "INSERT INTO processed_data (id, page_id, processor_name, data) VALUES (?, ?, ?, ?)",
            (processed_data_uuid, page_id, processor_name, data)
        )
        self.conn.commit()
        return processed_data_uuid

    def add_page_vector(self, page_id: str, vector: List[float], metadata: dict):
        try:
            self.page_collection.add(ids=[page_id], embeddings=[vector], metadatas=[metadata])
        except Exception as e:
            print(f"Error adding page vector to ChromaDB for page {page_id}: {e}")

    def add_summary_vector(self, processed_data_id: str, vector: List[float], metadata: dict):
        try:
            self.summary_collection.add(ids=[processed_data_id], embeddings=[vector], metadatas=[metadata])
        except Exception as e:
            print(f"Error adding summary vector to ChromaDB for {processed_data_id}: {e}")

    def add_sentence_vector(self, sentence_id: str, vector: List[float], metadata: dict):
        try:
            self.sentence_collection.add(ids=[sentence_id], embeddings=[vector], metadatas=[metadata])
        except Exception as e:
            print(f"Error adding sentence vector to ChromaDB for {sentence_id}: {e}")

    def get_sentence_by_id(self, sentence_id: str) -> Optional[Dict]:
        self.cursor.execute("SELECT * FROM sentences WHERE id = ?", (sentence_id,))
        return self.cursor.fetchone()

    def get_chapter_by_id(self, chapter_id: str) -> Optional[Dict]:
        self.cursor.execute("SELECT * FROM chapters WHERE id = ?", (chapter_id,))
        return self.cursor.fetchone()

    def get_processed_data_by_id(self, processed_data_id: str) -> Optional[Dict]:
        self.cursor.execute("SELECT * FROM processed_data WHERE id = ?", (processed_data_id,))
        return self.cursor.fetchone()

    def get_page_by_sentence_id(self, sentence_id: str) -> Optional[Dict]:
        query = """SELECT p.* FROM pages p
                 JOIN sentences s ON p.chapter_id = s.chapter_id
                 WHERE s.id = ? 
                 AND s.sentence_index_in_chapter >= (SELECT s2.sentence_index_in_chapter FROM sentences s2 WHERE s2.id = p.start_sentence_id)
                 AND s.sentence_index_in_chapter <= (SELECT s3.sentence_index_in_chapter FROM sentences s3 WHERE s3.id = p.end_sentence_id)
                 LIMIT 1"""
        self.cursor.execute(query, (sentence_id,))
        return self.cursor.fetchone()

    def get_pages_by_ids(self, page_ids: List[str]) -> List[Dict]:
        if not page_ids:
            return []
        placeholders = ', '.join('?' for _ in page_ids)
        query = f"SELECT * FROM pages WHERE id IN ({placeholders})"
        self.cursor.execute(query, page_ids)
        return self.cursor.fetchall()

    def find_pages_by_text(self, book_id: str, query_text: str, chapter_id: Optional[str] = None, limit: int = 10) -> List[Dict]:
        if chapter_id:
            query = "SELECT p.* FROM pages p WHERE p.chapter_id = ? AND p.content LIKE ? LIMIT ?"
            params = (chapter_id, f'%{query_text}%', limit)
        else:
            query = "SELECT p.* FROM pages p JOIN chapters c ON p.chapter_id = c.id WHERE c.book_id = ? AND p.content LIKE ? LIMIT ?"
            params = (book_id, f'%{query_text}%', limit)
        self.cursor.execute(query, params)
        return self.cursor.fetchall()

    def query_page_vectors(self, query_vector: List[float], book_id: Optional[str] = None, n_results: int = 10) -> List[str]:
        try:
            filter_metadata = {"book_id": book_id} if book_id else {}
            results = self.page_collection.query(query_embeddings=[query_vector], where=filter_metadata, n_results=n_results)
            return results.get('ids', [[]])[0]
        except Exception as e:
            print(f"Error querying page vectors from ChromaDB: {e}")
            return []

    def query_summary_vectors(self, query_vector: List[float], book_id: Optional[str] = None, n_results: int = 10) -> List[str]:
        try:
            filter_metadata = {"book_id": book_id} if book_id else {}
            results = self.summary_collection.query(query_embeddings=[query_vector], where=filter_metadata, n_results=n_results)
            return results.get('ids', [[]])[0]
        except Exception as e:
            print(f"Error querying summary vectors from ChromaDB: {e}")
            return []

    def query_sentence_vectors(self, query_vector: List[float], book_id: Optional[str] = None, n_results: int = 10) -> List[str]:
        try:
            filter_metadata = {"book_id": book_id} if book_id else {}
            results = self.sentence_collection.query(query_embeddings=[query_vector], where=filter_metadata, n_results=n_results)
            return results.get('ids', [[]])[0]
        except Exception as e:
            print(f"Error querying sentence vectors from ChromaDB: {e}")
            return []

    def close(self):
        if self.conn:
            self.conn.close()