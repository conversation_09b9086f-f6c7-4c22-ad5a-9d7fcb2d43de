# dev/llm/embedding.py
import requests
import time
import logging
from typing import List

from ..config import LLM_CONFIG

logger = logging.getLogger(__name__)

def get_embedding(text: str, provider: str = None) -> List[float]:
    """为给定文本生成向量嵌入。"""
    if provider is None:
        provider = LLM_CONFIG.get('embedding_provider', 'ollama_embedding')
    
    provider_config = LLM_CONFIG['providers'].get(provider)
    if not provider_config:
        raise ValueError(f"未找到嵌入服务提供商 '{provider}' 的配置")

    api_url = provider_config['api_url']
    model = provider_config['model']
    max_retries = provider_config.get('max_retries', 3)

    headers = {'Content-Type': 'application/json'}
    payload = {
        'model': model,
        'prompt': text
    }

    logger.debug(f"Embedding Call - Provider: {provider}, Model: {model}, Max Retries: {max_retries}")

    for attempt in range(max_retries):
        try:
            response = requests.post(api_url, headers=headers, json=payload, timeout=120)
            response.raise_for_status()
            data = response.json()
            logger.debug(f"Embedding Call - Attempt {attempt + 1} successful.")
            return data['embedding']
        except requests.exceptions.RequestException as e:
            logger.warning(f"请求嵌入API时出错 (尝试 {attempt + 1}/{max_retries}): {e}")
            if attempt < max_retries - 1:
                time.sleep(5) # 等待5秒后重试
        except Exception as e:
            logger.error(f"处理嵌入请求时发生意外错误: {e}")
            break # 发生其他错误则不重试

    raise Exception(f'在 {max_retries} 次尝试后，获取向量嵌入失败')
