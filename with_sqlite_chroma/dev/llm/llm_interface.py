# dev/llm/llm_interface.py
import requests
import time
import datetime
import re
import logging

from ..config import LLM_CONFIG, DEBUG_MODE

logger = logging.getLogger(__name__)

def query_llm(
    prompt: str,
    api_url: str = None,
    model: str = None,
    temperature: float = 0.0,
    max_tokens: int = 4096,
    max_retries: int = 5,
    provider: str = None
) -> str:
    """查询大语言模型 API，支持多种供应商。"""
    if provider is None:
        provider = LLM_CONFIG.get('default_provider', 'ollama')
    provider_config = LLM_CONFIG['providers'].get(provider)
    if not provider_config:
        raise ValueError(f"未找到提供商 '{provider}' 的配置")

    api_url = api_url or provider_config.get('api_url')
    model = model or provider_config.get('model')
    max_retries = max_retries or provider_config.get('max_retries', 3)
    max_tokens = max_tokens or provider_config.get('max_tokens', 4096)
    api_key = provider_config.get('api_key', '')

    headers = {'Content-Type': 'application/json'}
    if provider in ['deepseek', 'kimi'] and api_key:
        headers['Authorization'] = f'Bearer {api_key}'

    payload = {}
    if provider == 'ollama':
        payload = {
            'model': model,
            'messages': [{'role': 'user', 'content': prompt}],
            'stream': False,
            'options': {
                'temperature': temperature,
                'num_predict': max_tokens
            }
        }
    elif provider in ['deepseek', 'kimi']:
        payload = {
            'model': model,
            'messages': [{'role': 'user', 'content': prompt}],
            'temperature': temperature,
            'max_tokens': max_tokens
        }

    logger.debug(f"LLM Call - Provider: {provider}, Model: {model}, Max Retries: {max_retries}")

    for attempt in range(max_retries):
        logger.debug(f"LLM Call - Attempt {attempt + 1}/{max_retries}...")
        try:
            response = requests.post(api_url, headers=headers, json=payload, timeout=120)
            response.raise_for_status()
            data = response.json()
            logger.debug(f"LLM Call - Attempt {attempt + 1} successful.")
            if provider == 'ollama':
                return data['message']['content']
            elif provider in ['deepseek', 'kimi']:
                return data['choices'][0]['message']['content']
        except requests.exceptions.Timeout:
            logger.warning(f'{datetime.datetime.now()}: 请求超时 (尝试 {attempt + 1}/{max_retries})')
            if attempt < max_retries - 1:
                time.sleep(10)
        except requests.exceptions.HTTPError as e:
            if e.response.status_code == 429:
                logger.warning(f'{datetime.datetime.now()}: 达到速率限制，等待...')
                time.sleep(30)
            else:
                logger.error(f'{datetime.datetime.now()}: HTTP 错误: {e}')
                if attempt < max_retries - 1:
                    time.sleep(10)
        except Exception as e:
            logger.error(f'{datetime.datetime.now()}: 发生意外错误: {e}')
            if attempt < max_retries - 1:
                time.sleep(10)
    logger.error(f"LLM Call - All {max_retries} attempts failed.")
    raise Exception(f'查询 API 在 {max_retries} 次尝试后失败')

def clean_llm_response(response: str) -> str:
    """清理大语言模型的响应，移除"思考过程"的标签和其他不需要的构件。"""
    if not response:
        return ""
    response = re.sub(r'<think>.*?</think>', '', response, flags=re.DOTALL | re.IGNORECASE)
    response = re.sub(r'<thinking>.*?</thinking>', '', response, flags=re.DOTALL | re.IGNORECASE)
    response = re.sub(r'<thought>.*?</thought>', '', response, flags=re.DOTALL | re.IGNORECASE)
    response = re.sub(r'<reasoning>.*?</reasoning>', '', response, flags=re.DOTALL | re.IGNORECASE)
    response = re.sub(r'```thinking.*?```', '', response, flags=re.DOTALL | re.IGNORECASE)
    response = re.sub(r'```thought.*?```', '', response, flags=re.DOTALL | re.IGNORECASE)
    thinking_prefixes = [
        r'^(好的，我现在需要.*?。)',
        r'^(Let me think about this.*?…)',
        r'^(I need to.*?…)',
        r'^(First, I.*?…)',
        r'^(Okay, I.*?…)',
        r'^(好，我.*?。)',
        r'^(首先，我.*?。)',
    ]
    for prefix in thinking_prefixes:
        response = re.sub(prefix, '', response, flags=re.MULTILINE | re.IGNORECASE)
    response = re.sub(r'\n\s*\n\s*\n', '\n\n', response)
    response = response.strip()
    return response
