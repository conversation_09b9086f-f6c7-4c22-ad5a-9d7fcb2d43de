# dev/book_parsers/epub_parser.py
import os
import tempfile
import zipfile
import logging
import hashlib
from lxml import etree
from ..processing import text_utils

logger = logging.getLogger(__name__)

# --- Main Parser Function --- #

def parse_epub(file_path: str) -> dict:
    """解析EPUB，提取元数据、内容，并为每个句子生成丰富的上下文位置信息。"""
    logger.info(f"- Starting EPUB processing for: {file_path}")
    temp_dir = tempfile.TemporaryDirectory()
    
    try:
        with zipfile.ZipFile(file_path, "r") as zf:
            zf.extractall(temp_dir.name)

        # 1. 找到并解析 container.xml 以获取 OPF 文件的路径
        container_path = os.path.join(temp_dir.name, "META-INF", "container.xml")
        container_tree = etree.parse(container_path)
        ns = {'n': "urn:oasis:names:tc:opendocument:xmlns:container"}
        opf_path_rel = container_tree.xpath("//n:rootfile/@full-path", namespaces=ns)[0]
        opf_path_abs = os.path.join(temp_dir.name, opf_path_rel)
        opf_dir = os.path.dirname(opf_path_abs)

        # 2. 解析 OPF 文件以获取元数据、清单和书脊
        opf_tree = etree.parse(opf_path_abs)
        ns_opf = {'opf': "http://www.idpf.org/2007/opf", 'dc': "http://purl.org/dc/elements/1.1/"}
        
        metadata = {
            'title': opf_tree.xpath("//dc:title/text()", namespaces=ns_opf)[0],
            'author': opf_tree.xpath("//dc:creator/text()", namespaces=ns_opf)[0],
            'language': opf_tree.xpath("//dc:language/text()", namespaces=ns_opf)[0]
        }
        
        manifest_items = opf_tree.xpath("//opf:manifest/opf:item", namespaces=ns_opf)
        manifest = {item.get('id'): item.get('href') for item in manifest_items}
        
        spine_itemrefs = opf_tree.xpath("//opf:spine/opf:itemref", namespaces=ns_opf)
        spine_ids = [item.get('idref') for item in spine_itemrefs]

        # 3. 按真实章节处理书脊中的每一项
        chapters = []
        # 使用 lxml的HTML解析器
        parser = etree.HTMLParser()

        for i, idref in enumerate(spine_ids):
            item_href = manifest.get(idref)
            if not item_href:
                logger.warning(f"  - Could not find href for idref '{idref}' in manifest. Skipping.")
                continue

            item_path = os.path.join(opf_dir, item_href)
            logger.info(f"  - Processing spine item {i+1} (idref: {idref}): {item_href}")

            if not os.path.exists(item_path):
                logger.warning(f"  - File not found: {item_path}. Skipping.")
                continue

            with open(item_path, 'rb') as f:
                html_content = f.read()

            tree = etree.fromstring(html_content, parser)
            body = tree.find("body")
            if body is None:
                continue

            sentences_in_chapter = []
            # 遍历所有可能包含文本的元素
            content_tags_xpath = ".//p | .//h1 | .//h2 | .//h3 | .//h4 | .//h5 | .//h6 | .//li"
            for element in body.xpath(content_tags_xpath):
                element_text = ''.join(element.itertext()).strip()
                if not element_text:
                    continue

                # 将元素内的文本分割成句子
                sentences = text_utils.parse_text(element_text, metadata['language'])
                
                for sentence_text in sentences:
                    # 为每个句子创建丰富的上下文对象
                    sentence_hash = hashlib.sha256(sentence_text.encode('utf-8')).hexdigest()
                    
                    # 向上查找锚点ID
                    ancestor_id = None
                    for ancestor in element.iterancestors():
                        aid = ancestor.get('id')
                        if aid:
                            ancestor_id = aid
                            break

                    hints = {
                        'tag': element.tag,
                        'id': element.get('id'),
                        'class': element.get('class'),
                        'ancestor_id': ancestor_id
                    }

                    location_info = {
                        'spine_index': i,
                        'chapter_href': item_href,
                        'element_hints': hints
                    }

                    sentences_in_chapter.append({
                        'text': sentence_text,
                        'text_hash': sentence_hash,
                        'location': location_info
                    })

            if sentences_in_chapter:
                chapters.append({
                    'title': idref, # 使用idref作为临时标题
                    'content': sentences_in_chapter
                })

        return {
            'metadata': metadata,
            'chapters': chapters
        }

    finally:
        temp_dir.cleanup()
        logger.info("  - EPUB processing finished and temp directory cleaned up.")
