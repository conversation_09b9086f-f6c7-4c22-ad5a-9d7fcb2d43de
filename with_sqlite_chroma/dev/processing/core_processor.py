# dev/processing/core_processor.py
from typing import List, Dict, Optional, Tuple
import re
from concurrent.futures import ThreadPoolExecutor, as_completed
import logging
import hashlib
import pluggy

from ..llm.llm_interface import query_llm, clean_llm_response
from ..llm.embedding import get_embedding
from ..llm.prompt_manager import Prompt<PERSON>anager
from ..config import DEFAULT_CONFIG, LANGUAGE_MAPPING, LLM_CONFIG
from . import text_utils
from ..hooks import hookspecs
from ..system_plugins import default_processors
from .pipeline_objects import Page


logger = logging.getLogger(__name__)

prompt_manager = PromptManager()

def _process_page_in_thread(page_data: dict, detected_language: str, pm: pluggy.PluginManager) -> Page:
    page_id = page_data['id']
    page_content = page_data['content']
    page_object = Page(page_id=page_id, content=page_content)
    logger.info(f"  - [Thread] Invoking process_page hook for Page {page_id}...")
    pm.hook.process_page(page=page_object, detected_language=detected_language)
    return page_object

def get_prompt_with_language(template_name: str, language: str, **kwargs) -> str:
    template = prompt_manager.get_template(template_name)
    if not template:
        raise ValueError(f"未找到 '{template_name}' 提示词模板")
    language_instruction = LANGUAGE_MAPPING.get(language, 'English')
    return template.format(language=language_instruction, **kwargs)

def parse_pause_point(response: str) -> Optional[int]:
    response = clean_llm_response(response)
    try:
        match = re.search(r'Break point:\s*<(\d+)>', response, re.IGNORECASE)
        if match:
            return int(match.group(1))
        match = re.search(r'断点[:：]\s*<(\d+)>', response)
        if match:
            return int(match.group(1))
        match = re.search(r'<(\d+)>', response)
        if match:
            return int(match.group(1))
        patterns = [
            r'(?:break point|断点)[:：]?\s*(\d+)',
            r'(?:选择|choose|select)[:：]?\s*(\d+)',
            r'(?:标签|label|tag)[:：]?\s*(\d+)'
        ]
        for pattern in patterns:
            match = re.search(pattern, response, re.IGNORECASE)
            if match:
                return int(match.group(1))
        return None
    except (ValueError, AttributeError):
        return None

def create_page(
    sentences: List[Dict],
    start_idx: int,
    config: Dict,
    language: str
) -> Tuple[List[Dict], int]:
    unit_limit = config.get('unit_limit', 1000)
    start_threshold = config.get('start_threshold', 300)

    page_sentences_with_markers = [sentences[start_idx]['text']]
    unit_count = text_utils.count_text_units(sentences[start_idx]['text'], language)
    j = start_idx + 1

    while unit_count < unit_limit and j < len(sentences):
        segment_units = text_utils.count_text_units(sentences[j]['text'], language)
        unit_count += segment_units

        if unit_count >= start_threshold:
            page_sentences_with_markers.append(f"<{j}>")

        page_sentences_with_markers.append(sentences[j]['text'])
        j += 1

    page_sentences_with_markers.append(f"<{j}>")

    min_units = 100 if language == 'zh' else 50
    if unit_count < min_units:
        return sentences[start_idx:j], j

    try:
        preceding_context = ("" if start_idx == 0 else "...\n" +
            '\n'.join([s['text'] for s in sentences[max(0, start_idx-2):start_idx]]))
        next_preview = "" if j >= len(sentences) else sentences[j]['text'] + "\n..."

        prompt = get_prompt_with_language(
            'pagination',
            language,
            previous_context=preceding_context,
            current_passage='\n'.join(page_sentences_with_markers),
            next_preview=next_preview
        )
        logger.debug(f"create_page - Calling LLM for pagination (start_idx: {start_idx}, j: {j})...")
        response = query_llm(
            prompt,
            temperature=config.get('temperature', 0.0),
            provider=config.get('provider', LLM_CONFIG.get(
                'default_provider', 'ollama'))
        )
        logger.debug(f"create_page - LLM response received for pagination (start_idx: {start_idx}, j: {j}).")

        cleaned_response = clean_llm_response(response)
        pause_point = parse_pause_point(cleaned_response)

        if pause_point and start_idx < pause_point <= j:
            logger.info(f"create_page - Returning page with LLM-determined pause_point: {pause_point}")
            return sentences[start_idx: pause_point], pause_point
        else:
            logger.info(f"create_page - Returning page with default split (LLM response invalid or no valid pause_point).")
            return sentences[start_idx: j], j

    except Exception as e:
        if config.get('verbose', False):
            logger.warning(f"分页决策失败，使用默认切分: {e}")
        logger.error(f"create_page - Exception during LLM call for pagination: {e}. Returning default split.")
        return sentences[start_idx: j], j

def paginate_text(sentences: List[Dict], config: Dict, language: str, storage_manager) -> List[str]:
    total_sentences = len(sentences)
    added_page_ids = []
    i = 0
    page_index_in_chapter = 0

    while i < total_sentences:
        page_sentences, next_i = create_page(
            sentences, i, config, language)
        
        if not page_sentences:
            i = next_i
            continue

        page_content = "\n".join([s['text'] for s in page_sentences])
        start_sentence_id = page_sentences[0]['sentence_id']
        end_sentence_id = page_sentences[-1]['sentence_id']
        chapter_id = page_sentences[0]['chapter_id']

        page_id = storage_manager.add_page_only(
            chapter_id=chapter_id,
            page_index_in_chapter=page_index_in_chapter,
            content=page_content,
            start_sentence_id=start_sentence_id,
            end_sentence_id=end_sentence_id
        )
        added_page_ids.append(page_id)
        logger.info(f"    - Added Page {page_index_in_chapter} (ID: {page_id}) to database.")

        i = next_i
        page_index_in_chapter += 1

    return added_page_ids

def process_book(book_data: dict, storage_manager):
    pm = pluggy.PluginManager("read_agent")
    pm.add_hookspecs(hookspecs)
    pm.register(default_processors)

    logger.info("\n[Step 1/5] Adding book and chapters to database...")
    
    logger.info(f"  - Calculating hash for {book_data['source_path']}...")
    sha256_hash = hashlib.sha256()
    with open(book_data['source_path'], "rb") as f:
        for byte_block in iter(lambda: f.read(4096), b""):
            sha256_hash.update(byte_block)
    content_hash = sha256_hash.hexdigest()
    logger.info(f"  - Calculated content hash: {content_hash[:16]}...")

    detected_language = book_data['metadata'].get('language', 'en')

    book_id = storage_manager.add_book(
        source_path=book_data['source_path'],
        content_hash=content_hash,
        detected_language=detected_language,
        metadata=book_data['metadata']
    )

    all_sentences_in_book = []

    logger.info("\n[Step 2/5] Adding sentences and vectorizing them...")
    for i, chapter in enumerate(book_data['chapters']):
        if not chapter['content']:
            logger.info(f"  - Chapter {i+1} '{chapter['title']}' is empty, skipping.")
            continue
        
        first_sentence_loc = chapter['content'][0]['location']
        spine_index = first_sentence_loc['spine_index']
        chapter_href = first_sentence_loc['chapter_href']

        chapter_id = storage_manager.add_chapter_only(
            book_id=book_id,
            chapter_index=i,
            title=chapter['title'],
            content="\n".join([s['text'] for s in chapter['content']]),
            spine_index=spine_index,
            chapter_href=chapter_href
        )
        logger.info(f"  - Added Chapter {i+1} '{chapter['title']}' (ID: {chapter_id}).")

        for s_idx, sentence in enumerate(chapter['content']):
            loc = sentence['location']
            sentence_id = storage_manager.add_sentence_only(
                chapter_id=chapter_id,
                sentence_index_in_chapter=s_idx,
                content=sentence['text'],
                text_hash=sentence['text_hash'],
                element_hints=loc['element_hints']
            )
            
            try:
                logger.debug(f"  - Vectorizing sentence {sentence_id}...")
                sentence_vector = get_embedding(sentence['text'])
                metadata = {"book_id": book_id, "chapter_id": chapter_id}
                storage_manager.add_sentence_vector(sentence_id, sentence_vector, metadata)
            except Exception as e:
                logger.error(f"Failed to vectorize sentence {sentence_id}: {e}")

            all_sentences_in_book.append({
                'sentence_id': sentence_id,
                'chapter_id': chapter_id,
                'text': sentence['text']
            })
    logger.info("   - Chapters and sentences (with vectors) added.")

    logger.info("\n[Step 3/5] Paginating sentences and adding pages to database...")
    added_page_ids = paginate_text(
        all_sentences_in_book,
        DEFAULT_CONFIG[detected_language],
        detected_language,
        storage_manager
    )
    
    all_pages_to_process = storage_manager.get_pages_by_ids(added_page_ids)
    logger.info("   - Pages retrieved for processing.")

    logger.info("\n[Step 4/5] Processing and Vectorizing Summaries (concurrently)...")
    with ThreadPoolExecutor(max_workers=5) as executor:
        futures = [executor.submit(_process_page_in_thread, page_data, detected_language, pm) for page_data in all_pages_to_process]
        for future in as_completed(futures):
            try:
                processed_page_object = future.result()
                if processed_page_object.processed_data:
                    for processor_name, result_data in processed_page_object.processed_data.items():
                        processed_data_id = storage_manager.add_processed_data(
                            processed_page_object.page_id, 
                            processor_name, 
                            result_data
                        )
                        logger.info(f"    - [Thread] Saved result from '{processor_name}' for Page {processed_page_object.page_id}.")

                        if processor_name == 'gisting':
                            try:
                                logger.info(f"    - [Thread] Vectorizing summary for Page {processed_page_object.page_id}...")
                                summary_vector = get_embedding(result_data)
                                metadata = {"book_id": book_id, "page_id": processed_page_object.page_id}
                                storage_manager.add_summary_vector(processed_data_id, summary_vector, metadata)
                                logger.info(f"    - [Thread] Summary vector for Page {processed_page_object.page_id} added.")
                            except Exception as e:
                                logger.error(f"    - [Thread] Failed to vectorize summary for page {processed_page_object.page_id}: {e}")

            except Exception as exc:
                logger.error(f'A page processing task generated an exception: {exc}', exc_info=True)
    logger.info("   - Page processing and summary vectorization finished.")

    logger.info("\n[Step 5/5] Vectorizing Page Content...")
    for page_data in all_pages_to_process:
        try:
            page_id = page_data['id']
            logger.debug(f"  - Vectorizing page {page_id}...")
            vector = get_embedding(page_data['content'])
            metadata = {"book_id": book_id, "chapter_id": page_data['chapter_id']}
            storage_manager.add_page_vector(page_id, vector, metadata)
            logger.info(f"  - Vector for page {page_id} added to ChromaDB.")
        except Exception as e:
            logger.error(f"Failed to vectorize page {page_id}: {e}")
    logger.info("   - Page content vectorization finished.")

    logger.info("\n--- All processing steps completed ---")