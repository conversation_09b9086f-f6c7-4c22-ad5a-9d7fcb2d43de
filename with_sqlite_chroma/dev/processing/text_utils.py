# dev/processing/text_utils.py
import re
from typing import List, Dict

# 用于文本切分的标点符号集
SENTENCE_ENDINGS = {
    'primary': ['。', '！', '？', '……'],           # 主要的句子结束符（强分割）
    'secondary': ['；', '：'],                     # 次要的句子结束符（中等分割）
    'english': ['.', '!', '?'],                   # 英文的句子结束符
    # 混合模式下所有可能的结束符
    'mixed': ['。', '！', '？', '；', '：', '……', '.', '!', '?', ';', ':']
}

# 定义一个从右引号/括号到左引号/括号的映射。
QUOTE_MAP = {
    '”': '“', '’': '‘', '"': '"', "'": "'",
    '』': '『', '》': '《', ')': '(', '）': '（'
}
OPENING_QUOTES = set(QUOTE_MAP.values())
CLOSING_QUOTES = set(QUOTE_MAP.keys())
AMBIGUOUS_QUOTES = {"\"", "'"}

def detect_language(text: str) -> str:
    """通过字符分析自动检测文本语言。"""
    if not text.strip():
        return 'en'
    chinese_chars = len(re.findall(r'[\u4e00-\u9fff]', text))
    total_chars = len(re.sub(r'\s', '', text))
    if total_chars == 0:
        return 'en'
    chinese_ratio = chinese_chars / total_chars
    return 'zh' if chinese_ratio > 0.1 else 'en'

def count_text_units(text: str, language: str) -> int:
    """根据指定语言计算文本单位。"""
    if language == 'zh':
        units = 0
        i = 0
        while i < len(text):
            char = text[i]
            if '\u4e00' <= char <= '\u9fff':
                units += 1
                i += 1
            elif char.isalpha():
                start_i = i
                while i < len(text) and (text[i].isalnum() or text[i] in ' -_'):
                    i += 1
                if i > start_i:
                    units += 1
            elif char.isdigit():
                while i < len(text) and (text[i].isdigit() or text[i] in '.'):
                    i += 1
                units += 0.5
            else:
                units += 0.1
                i += 1
        return int(units)
    else:
        return len(text.split())

def get_text_statistics(text: str) -> Dict:
    """获取全面的文本统计信息。"""
    language = detect_language(text)
    return {
        'language': language,
        'total_units': count_text_units(text, language),
        'total_characters': len(text),
        'total_lines': len(text.split('\n')),
        'paragraphs': len([p for p in text.split('\n') if p.strip()]),
        'chinese_chars': len(re.findall(r'[\u4e00-\u9fff]', text)),
        'english_words': len(re.findall(r'[a-zA-Z]+', text))
    }

def is_sentence_ending(text: str, pos: int, language: str) -> bool:
    """判断在给定位置的标点符号是否代表句子结束。"""
    if pos + 1 >= len(text):
        return True
    char = text[pos]
    next_char = text[pos + 1]
    if char == '.' and language == 'en':
        if pos >= 2:
            prev_chars = text[max(0, pos-3):pos]
            common_abbrevs = ['Mr', 'Mrs', 'Dr', 'Prof', 'etc', 'vs', 'Inc', 'Ltd']
            for abbrev in common_abbrevs:
                if prev_chars.endswith(abbrev):
                    return False
        if next_char.isspace() and pos + 2 < len(text) and text[pos + 2].isupper():
            return True
        if '\u4e00' <= next_char <= '\u9fff':
            return True
    if char in ['。', '！', '？', '……']:
        return True
    if char in ['；', '：'] and next_char.isspace():
        return True
    return False

def split_by_punctuation(text: str, language: str) -> List[str]:
    """使用基于栈的方法按标点符号切分文本。"""
    if not text.strip():
        return []
    segments = []
    current_segment = ""
    quote_stack = []
    i = 0
    while i < len(text):
        char = text[i]
        current_segment += char
        if char in OPENING_QUOTES:
            if char in AMBIGUOUS_QUOTES and quote_stack and quote_stack[-1] == char:
                quote_stack.pop()
            else:
                quote_stack.append(char)
        elif char in CLOSING_QUOTES:
            if quote_stack and quote_stack[-1] == QUOTE_MAP[char]:
                quote_stack.pop()
        if not quote_stack and char in SENTENCE_ENDINGS['mixed'] and is_sentence_ending(text, i, language):
            end_pos = i
            while end_pos + 1 < len(text) and (text[end_pos + 1].isspace() or text[end_pos + 1] in CLOSING_QUOTES):
                end_pos += 1
                current_segment += text[end_pos]
            segments.append(current_segment.strip())
            current_segment = ""
            i = end_pos
        i += 1
    if current_segment.strip():
        segments.append(current_segment.strip())
    return [seg for seg in segments if seg.strip()]

def split_text_by_paragraphs(text: str) -> List[str]:
    """按段落切分文本。"""
    text = re.sub(r'\r\n', '\n', text)
    paragraphs = re.split(r'\n\s*\n', text)
    result = []
    for para in paragraphs:
        para = para.strip()
        if para:
            sub_paras = re.split(r'\n(?=\s*[　\t])', para)
            result.extend([p.strip() for p in sub_paras if p.strip()])
    return result

def parse_text(text: str, language: str) -> List[str]:
    """通用的文本解析函数，结合了按段落和按标点两种切分方式。"""
    paragraphs = split_text_by_paragraphs(text)
    segments = []
    for para in paragraphs:
        para_segments = split_by_punctuation(para, language)
        segments.extend(para_segments)
    return segments
