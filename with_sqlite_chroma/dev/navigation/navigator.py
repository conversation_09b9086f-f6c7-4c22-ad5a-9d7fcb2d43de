# dev/navigation/navigator.py
import json
import hashlib
import re
import logging
from typing import Optional, List, Dict
from collections import defaultdict
import difflib

from ..storage.storage_manager import StorageManager
from .location_model import Location
from ..llm.embedding import get_embedding

logger = logging.getLogger(__name__)

# 定义一个初步的停用词列表
STOP_WORDS = [
    'a', 'an', 'the', 'is', 'are', 'was', 'were', 'of', 'in', 'on', 'at', 'for', 'to',
    '的', '了', '是', '我', '你', '他', '她', '它', '一个', '一条', '这个', '那个', 
    '和', '与', '在', '也', '有关', '关于', '内容'
]

class Navigator:
    def __init__(self, storage_manager: StorageManager):
        self.storage = storage_manager

    def _extract_keywords(self, query_text: str) -> str:
        stop_words_pattern = r'\b(' + '|'.join(re.escape(word) for word in STOP_WORDS) + r')\b'
        keywords = re.sub(stop_words_pattern, '', query_text, flags=re.IGNORECASE)
        return " ".join(keywords.split())

    def _get_page_id_for_sentence(self, sentence_id: str) -> Optional[str]:
        page = self.storage.get_page_by_sentence_id(sentence_id)
        return page['id'] if page else None

    def _get_page_id_for_summary(self, summary_id: str) -> Optional[str]:
        processed_data = self.storage.get_processed_data_by_id(summary_id)
        return processed_data['page_id'] if processed_data else None

    def _fuse_results(self, results: Dict[str, List[str]], k: int = 60) -> List[tuple[str, float]]:
        fused_scores = defaultdict(float)
        for source, ranked_ids in results.items():
            for i, doc_id in enumerate(ranked_ids):
                if doc_id:
                    rank = i + 1
                    fused_scores[doc_id] += 1 / (k + rank)
        
        return sorted(fused_scores.items(), key=lambda item: item[1], reverse=True)

    def _build_location_from_page(self, page: dict, book_id: str) -> Optional[Location]:
        if not page:
            return None
        start_sentence = self.storage.get_sentence_by_id(page['start_sentence_id'])
        if not start_sentence:
            return None

        chapter = self.storage.get_chapter_by_id(page['chapter_id'])
        if not chapter:
            return None

        snippet = page['content'][:250]
        snippet_hash = hashlib.sha256(snippet.encode('utf-8')).hexdigest()

        return Location(
            book_id=book_id,
            chapter_id=page['chapter_id'],
            page_id=page['id'],
            sentence_id=page['start_sentence_id'],
            spine_index=chapter['spine_index'],
            chapter_href=chapter['chapter_href'],
            element_hints=json.loads(start_sentence['element_hints']),
            text_snippet=snippet,
            text_hash=snippet_hash
        )

    def find_location_by_text(self, book_id: str, query_text: str, context_location: Optional[Location] = None) -> Optional[Location]:
        logger.info(f"Hybrid search started for: '{query_text}'")
        chapter_id_to_search = context_location.chapter_id if context_location else None

        # --- Path A: Keyword Search (Fast Path) ---
        logger.info("Attempting keyword search...")
        keywords = self._extract_keywords(query_text) or query_text
        candidate_pages = self.storage.find_pages_by_text(
            book_id=book_id, 
            query_text=keywords, 
            chapter_id=chapter_id_to_search,
            limit=10
        )

        if not candidate_pages and chapter_id_to_search:
            candidate_pages = self.storage.find_pages_by_text(book_id=book_id, query_text=keywords, limit=10)

        if candidate_pages:
            logger.info(f"Keyword search found {len(candidate_pages)} candidates. Checking quality...")
            best_page = None
            highest_score = 0.0
            for page in candidate_pages:
                similarity = difflib.SequenceMatcher(None, query_text, page['content']).ratio()
                if similarity > highest_score:
                    highest_score = similarity
                    best_page = page
            
            # If we have a high-quality match, return it immediately.
            if highest_score > 0.5:
                logger.info(f"High-quality keyword match found (score: {highest_score:.4f}). Returning early.")
                return self._build_location_from_page(best_page, book_id)

        # --- Path B: Multi-layered Vector Search (Fallback) ---
        logger.info("Keyword search failed or result quality is low. Falling back to multi-layered vector search...")
        try:
            query_vector = get_embedding(query_text)
            
            page_ids_from_content = self.storage.query_page_vectors(query_vector, book_id, n_results=10)
            summary_ids = self.storage.query_summary_vectors(query_vector, book_id, n_results=10)
            sentence_ids = self.storage.query_sentence_vectors(query_vector, book_id, n_results=10)

            page_ids_from_summaries = [self._get_page_id_for_summary(sid) for sid in summary_ids]
            page_ids_from_sentences = [self._get_page_id_for_sentence(sid) for sid in sentence_ids]
            
            all_results = {
                'content': page_ids_from_content,
                'summary': [pid for pid in page_ids_from_summaries if pid],
                'sentence': [pid for pid in page_ids_from_sentences if pid]
            }
            fused_ranked_pages = self._fuse_results(all_results)

            if not fused_ranked_pages:
                logger.warning("No results found after RRF fusion.")
                return None

            best_page_id, top_score = fused_ranked_pages[0]
            logger.info(f"Best page found via fusion: {best_page_id} with RRF score {top_score:.4f}")
            
            best_page_data_list = self.storage.get_pages_by_ids([best_page_id])
            if not best_page_data_list:
                return None

            return self._build_location_from_page(best_page_data_list[0], book_id)

        except Exception as e:
            logger.error(f"An error occurred during vector search fallback: {e}", exc_info=True)
            return None