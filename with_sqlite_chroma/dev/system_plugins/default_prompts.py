# dev/system_plugins/default_prompts.py
import pluggy

hookimpl = pluggy.HookimplMarker("read_agent")

# 通用提示模板，使用 {language} 变量以支持多语言
UNIVERSAL_PROMPT_TEMPLATES = {
    # 分页提示：要求模型在自然断点处切分文本
    'pagination': """You are given a passage with numbered labels between paragraphs.
Please choose one label that marks a natural break point.
Such points can be scene transitions, end of dialogues, topic changes, etc.

Please respond in {language}.

Previous context: {previous_context}

Current passage:
{current_passage}

Next preview: {next_preview}

Please answer with "Break point: <number>" and explain your choice.""",

    # Gisting (摘要) 提示：要求模型缩短段落，保留关键信息
    'gisting': """Please shorten the following passage while preserving key information.
Just provide the shortened version without explanation.

Please respond in {language}.

Passage:
{page_text}""",

    # 查找提示：要求模型根据摘要和查询，判断哪些页面最相关
    'lookup': """Based on the following document summary and user query, determine which pages would be most relevant to review.

Please respond in {language}.

Document Summary:
{gist_context}

User Query: {query}

Please respond with page numbers in format: [1, 3, 5] and explain your reasoning briefly.""",

    # 响应生成提示：要求模型根据上下文，全面回答用户查询
    'response': """Based on the following context from a long document, please provide a comprehensive answer to the user's query.

Please respond in {language}.

Context:
{context}

User Query: {query}

Instructions:
- Provide a detailed, informative response
- Structure your answer clearly with headings or bullet points when appropriate
- Reference the document content explicitly
- Ensure accuracy and avoid speculation beyond the context
- If context is insufficient, state this clearly

Answer:"""
}

@hookimpl
def register_prompts() -> dict:
    """提供系统默认的核心提示词模板。"""
    return UNIVERSAL_PROMPT_TEMPLATES
