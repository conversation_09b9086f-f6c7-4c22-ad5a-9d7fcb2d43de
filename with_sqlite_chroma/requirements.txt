annotated-types==0.7.0
anyio==4.10.0
asttokens @ file:///home/<USER>/feedstock_root/build_artifacts/asttokens_1733250440834/work
attrs==25.3.0
backoff==2.2.1
bcrypt==4.3.0
beautifulsoup4==4.13.4
build==1.3.0
cachetools==5.5.2
certifi==2025.6.15
charset-normalizer==3.4.2
chromadb==1.0.16
click==8.2.1
coloredlogs==15.0.1
comm @ file:///home/<USER>/feedstock_root/build_artifacts/comm_1733502965406/work
debugpy @ file:///croot/debugpy_1736267418885/work
decorator @ file:///home/<USER>/feedstock_root/build_artifacts/decorator_1740384970518/work
distro==1.9.0
durationpy==0.10
EbookLib==0.19
exceptiongroup @ file:///home/<USER>/feedstock_root/build_artifacts/exceptiongroup_1746947292760/work
executing @ file:///home/<USER>/feedstock_root/build_artifacts/executing_1745502089858/work
filelock==3.18.0
flatbuffers==25.2.10
fsspec==2025.7.0
google-auth==2.40.3
googleapis-common-protos==1.70.0
grpcio==1.74.0
h11==0.16.0
hf-xet==1.1.7
httpcore==1.0.9
httptools==0.6.4
httpx==0.28.1
huggingface-hub==0.34.4
humanfriendly==10.0
idna==3.10
importlib_metadata @ file:///home/<USER>/feedstock_root/build_artifacts/bld/rattler-build_importlib-metadata_1747934053/work
importlib_resources==6.5.2
ipykernel @ file:///home/<USER>/feedstock_root/build_artifacts/ipykernel_1719845459717/work
ipython @ file:///home/<USER>/feedstock_root/build_artifacts/bld/rattler-build_ipython_1748713870/work
ipython_pygments_lexers @ file:///home/<USER>/feedstock_root/build_artifacts/ipython_pygments_lexers_1737123620466/work
jedi @ file:///home/<USER>/feedstock_root/build_artifacts/jedi_1733300866624/work
jsonschema==4.25.0
jsonschema-specifications==2025.4.1
jupyter_client @ file:///home/<USER>/feedstock_root/build_artifacts/jupyter_client_1733440914442/work
jupyter_core @ file:///home/<USER>/feedstock_root/build_artifacts/jupyter_core_1748333051527/work
kubernetes==33.1.0
lxml==5.4.0
markdown-it-py==4.0.0
matplotlib-inline @ file:///home/<USER>/feedstock_root/build_artifacts/matplotlib-inline_1733416936468/work
mdurl==0.1.2
mmh3==5.2.0
mpmath==1.3.0
nest_asyncio @ file:///home/<USER>/feedstock_root/build_artifacts/nest-asyncio_1733325553580/work
numpy==2.3.2
oauthlib==3.3.1
onnxruntime==1.22.1
opentelemetry-api==1.36.0
opentelemetry-exporter-otlp-proto-common==1.36.0
opentelemetry-exporter-otlp-proto-grpc==1.36.0
opentelemetry-proto==1.36.0
opentelemetry-sdk==1.36.0
opentelemetry-semantic-conventions==0.57b0
orjson==3.11.1
overrides==7.7.0
packaging @ file:///home/<USER>/feedstock_root/build_artifacts/bld/rattler-build_packaging_1745345660/work
parso @ file:///home/<USER>/feedstock_root/build_artifacts/parso_1733271261340/work
pexpect @ file:///home/<USER>/feedstock_root/build_artifacts/pexpect_1733301927746/work
pickleshare @ file:///home/<USER>/feedstock_root/build_artifacts/pickleshare_1733327343728/work
platformdirs @ file:///home/<USER>/feedstock_root/build_artifacts/bld/rattler-build_platformdirs_1746710438/work
pluggy==1.6.0
posthog==5.4.0
prompt_toolkit @ file:///home/<USER>/feedstock_root/build_artifacts/prompt-toolkit_1744724089886/work
protobuf==6.31.1
psutil @ file:///croot/psutil_1736367091698/work
ptyprocess @ file:///home/<USER>/feedstock_root/build_artifacts/ptyprocess_1733302279685/work/dist/ptyprocess-0.7.0-py2.py3-none-any.whl#sha256=92c32ff62b5fd8cf325bec5ab90d7be3d2a8ca8c8a3813ff487a8d2002630d1f
pure_eval @ file:///home/<USER>/feedstock_root/build_artifacts/pure_eval_1733569405015/work
pyasn1==0.6.1
pyasn1_modules==0.4.2
pybase64==1.4.2
pydantic==2.11.7
pydantic_core==2.33.2
Pygments @ file:///home/<USER>/feedstock_root/build_artifacts/pygments_1750615794071/work
PyPika==0.48.9
pyproject_hooks==1.2.0
python-dateutil @ file:///home/<USER>/feedstock_root/build_artifacts/bld/rattler-build_python-dateutil_1751104122/work
python-dotenv==1.1.1
PyYAML==6.0.2
pyzmq @ file:///croot/pyzmq_1734687138743/work
referencing==0.36.2
requests==2.32.4
requests-oauthlib==2.0.0
rich==14.1.0
rpds-py==0.27.0
rsa==4.9.1
setuptools==78.1.1
shellingham==1.5.4
six @ file:///home/<USER>/feedstock_root/build_artifacts/six_1733380938961/work
sniffio==1.3.1
soupsieve==2.7
stack_data @ file:///home/<USER>/feedstock_root/build_artifacts/stack_data_1733569443808/work
sympy==1.14.0
tenacity==9.1.2
tokenizers==0.21.4
tornado @ file:///croot/tornado_1748956929273/work
tqdm==4.67.1
traitlets @ file:///home/<USER>/feedstock_root/build_artifacts/traitlets_1733367359838/work
typer==0.16.0
typing-inspection==0.4.1
typing_extensions @ file:///home/<USER>/feedstock_root/build_artifacts/bld/rattler-build_typing_extensions_1748959427/work
urllib3==2.5.0
uvicorn==0.35.0
uvloop==0.21.0
watchfiles==1.1.0
wcwidth @ file:///home/<USER>/feedstock_root/build_artifacts/wcwidth_1733231326287/work
websocket-client==1.8.0
websockets==15.0.1
wheel==0.45.1
yapf==0.43.0
zipp @ file:///home/<USER>/feedstock_root/build_artifacts/zipp_1749421620841/work
