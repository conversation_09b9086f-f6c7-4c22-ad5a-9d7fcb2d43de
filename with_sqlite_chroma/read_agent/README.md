# Read Agent - 通用长文本处理脚本

本项目是 `read_agent.py`，一个受人类阅读模式启发的长文本处理工具。它基于论文 "A Human-Inspired Reading Agent with Gist Memory of Very Long Contexts" (<PERSON> et al., 2024) 的核心思想，旨在通过模拟“先浏览、再详读”的模式，高效地理解和查询大型文档。

该脚本完全由本地大语言模型（LLM）驱动，无需依赖外部 API，并内置了强大的缓存机制，以优化重复查询的性能。

## 核心特性

- **长文本处理**: 能够处理远超大语言模型单次输入限制的文本长度。
- **多语言支持**: 无缝处理中文、英文以及中英混合的文本，并能自动检测语言。
- **智能分页**: 使用基于标点和段落的智能切分算法，并借助 LLM寻找自然的语义断点，将长文本切分为连贯的“页面”。
- **摘要记忆 (Gist Memory)**: 为每个页面生成简短的核心摘要，形成对整个文档的快速索引。
- **高效缓存**: 首次处理文件后，会自动生成包含页面和摘要的 `.pkl` 缓存文件。当再次查询同一未修改文件时，直接从缓存加载，极大提升处理速度。
- **本地模型驱动**: 完全依赖本地运行的大语言模型服务（如 Ollama），确保数据隐私和低成本运行。

## 工作流程

Read Agent 的工作流程模拟了人类阅读和查找信息的过程，分为以下四个主要步骤：

1.  **分页 (Pagination)**
    - 脚本首先将原始文本智能地切分为一系列文本片段。
    - 接着，它将片段聚合成达到预设长度的“页面”，并调用 LLM 来识别最佳的语义断点（如场景切换、话题结束），确保每个页面的内容连贯。

2.  **摘要 (Gisting)**
    - 对每个页面，脚本会再次调用 LLM，要求其生成一段高度浓缩的摘要（Gist Memory）。
    - 所有页面的摘要共同构成了对整个文档的“记忆大纲”。

3.  **查找 (Lookup)**
    - 当用户提出查询时，脚本会将所有页面的“摘要大纲”和用户查询一同发送给 LLM。
    - LLM 会判断哪些页面的摘要与查询最相关，并返回这些页面的编号。

4.  **响应 (Response)**
    - 脚本根据上一步返回的页面编号，提取这些相关页面的**完整原文**。
    - 最后，它将“摘要大纲”（提供全局背景）和相关页面的“详细原文”共同作为上下文，连同用户最初的查询，一起交给 LLM 生成最终的、详尽的回答。

## 环境要求

1.  **Python 3**: 脚本需要 Python 3 环境。
2.  **依赖库**: 需要 `requests` 库与本地 LLM 服务通信。
    ```bash
    pip install requests
    ```
3.  **本地 LLM 服务**: 必须有一个正在运行的、兼容 OpenAI API 格式的本地大语言模型服务，例如 [Ollama](https://ollama.com/)。

## 配置

脚本的配置在文件顶部的两个字典中完成：

-   `API_CONFIG`:
    -   `api_url`: 你的本地 LLM 服务的 API 地址（默认为 `http://localhost:11434/api/chat`）。
    -   `model`: 你希望使用的模型名称（默认为 `qwen3:8b`）。请确保你已在本地部署此模型。
    -   `max_retries`: API 请求失败时的重试次数。

-   `DEFAULT_CONFIG`:
    -   为中文 (`zh`) 和英文 (`en`) 分别定义了处理参数。
    -   `unit_limit`: 分页时，每个页面的大致单位（中文为混合单位，英文为单词数）上限。
    -   `start_threshold`: 页面内容达到多少单位后开始请求 LLM 寻找断点。
    -   `max_lookup_pages`: 在“查找”阶段最多返回的相关页面数量。

## 使用方法

你可以将 `read_agent.py` 作为模块导入到你自己的 Python 脚本中使用。

1.  **准备文件**:
    -   将 `read_agent.py` 放在你的项目目录下。
    -   准备一个你想要查询的文本文件，例如 `my_document.txt`。

2.  **创建调用脚本**:
    创建一个 `example.py` 文件，内容如下：

    ```python
    import read_agent as ra
    import time

    # 定义文件路径和你的查询
    file_to_process = 'path/to/your/my_document.txt'
    user_query = "文档中关于气候变化的主要观点是什么？"

    print(f"正在处理文件: {file_to_process}")
    print(f"查询: {user_query}\n")

    start_time = time.time()

    # 调用核心处理函数
    # verbose=True 可以打印详细的处理日志
    # force_reprocess=True 可以强制重新处理，忽略缓存
    result = ra.process_text_file(
        file_path=file_to_process,
        query=user_query,
        verbose=True,
        force_reprocess=False
    )

    end_time = time.time()

    print("\n==================== 查询结果 ====================")
    print(result['response'])
    print("==================================================\n")

    # 打印统计信息
    stats = result['statistics']
    print(f"语言: {result['language']}")
    print(f"处理用时: {end_time - start_time:.2f} 秒")
    print(f"是否使用缓存: {result['cache_used']}")
    print(f"摘要压缩率: {result['compression_rate']:.2%}")
    print(f"总单位数: {stats['total_units']}, 总字符数: {stats['total_characters']}")
    print(f"相关页面: {result['relevant_pages']}")
    ```

3.  **运行**:
    ```bash
    python example.py
    ```

## 缓存机制

-   **创建**: 首次成功处理一个文本文件（如 `my_document.txt`）后，脚本会在同一目录下自动创建一个名为 `my_document.pkl` 的缓存文件。
-   **验证**: 当你再次处理 `my_document.txt` 时，脚本会计算当前文件的哈希值，并与缓存文件中的哈希值进行比较。
-   **重用**: 如果哈希值匹配（意味着文件未被修改），脚本将直接从 `.pkl` 文件中加载分页和摘要结果，跳过最耗时的步骤，仅执行“查找”和“响应”流程。
-   **更新**: 如果哈希值不匹配，脚本会删除旧的缓存，重新进行完整处理，并生成新的缓存。
-   **强制刷新**: 你可以通过在调用 `process_text_file` 时设置 `force_reprocess=True` 来强制忽略缓存，进行全新的处理。
