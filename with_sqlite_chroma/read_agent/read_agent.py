"""
Read Agent Universal - 中英文长文本处理通用实现

本项目是 Read Agent 算法的统一实现，旨在处理中英文长文本，并无缝支持混合语言内容。
该实现基于 Lee 等人 (2024) 的论文 "A Human-Inspired Reading Agent with Gist Memory of Very Long Contexts"。

主要特性:
- 基于标点的文本切分
- 使用语言变量的统一提示模板
- 自动语言检测
- 支持中英混合内容
- 无外部自然语言处理（NLP）依赖

使用方法:
    import read_agent as ra
    
    text = "你的长文档内容..."
    query = "讨论的主要观点是什么？"
    
    result = ra.process_long_text(text, query)
    print(result['response'])
"""

# 导入标准库和第三方库
import json                 # 用于处理 JSON 数据
import pickle               # 用于序列化和反序列化 Python 对象（缓存）
import re                   # 用于正则表达式操作
import time                 # 用于处理时间相关操作，如延迟
import datetime             # 用于处理日期和时间
import os                   # 用于与操作系统交互，如文件路径操作
from typing import List, Dict, Optional, Tuple, Union  # 用于类型提示
import requests             # 用于发送 HTTP 请求到本地模型 API
from concurrent.futures import ThreadPoolExecutor, as_completed

# ==================== 1. 配置和常量 (Configuration and Constants) ====================

# 默认处理配置，区分中英文
DEFAULT_CONFIG = {
    'zh': {
        'unit_limit': 1000,         # 分页时每页的单位上限（混合单位）
        'start_threshold': 300,    # 开始寻找断点标记的单位阈值
        'max_lookup_pages': 5,      # 查找相关页面时返回的最大页面数
        'temperature': 0.0,         # LLM 生成时的温度参数，0.0 表示更确定的输出
        'unit_type': 'char_mixed'   # 中文的单位计数类型：混合模式
    },
    'en': {
        'unit_limit': 1000,         # 分页时每页的单位上限（单词数）
        'start_threshold': 300,    # 开始寻找断点标记的单位阈值
        'max_lookup_pages': 5,      # 查找相关页面时返回的最大页面数
        'temperature': 0.0,         # LLM 生成时的温度参数
        'unit_type': 'word'         # 英文的单位计数类型：单词
    }
}

# LLM API 配置
LLM_CONFIG = {
    "default_provider": "deepseek",
    "providers": {
        "ollama": {
            "api_url": "http://localhost:11434/api/chat",
            "model": "qwen3:8b",
            "max_tokens": 4096,
            "max_retries": 5
        },
        "deepseek": {
            "api_url": "https://api.deepseek.com/chat/completions",
            "api_key": "sk-4c4b628af3244744913b0bc7cc10fe42",
            "model": "deepseek-chat",
            "max_tokens": 4096,
            "max_retries": 3
        },
        "kimi": {
            "api_url": "https://api.moonshot.cn/v1/chat/completions",
            "api_key": "sk-53NLn1dYrt5UQVDTVGPCNzEtX6gR7wvunL0JBX3UV0R0HONN",
            "model": "kimi-k2-0711-preview",
            "max_tokens": 4096,
            "max_retries": 3
        }
    }
}

# 通用提示模板，使用 {language} 变量以支持多语言
UNIVERSAL_PROMPT_TEMPLATES = {
    # 分页提示：要求模型在自然断点处切分文本
    'pagination': """You are given a passage with numbered labels between paragraphs.
Please choose one label that marks a natural break point.
Such points can be scene transitions, end of dialogues, topic changes, etc.

Please respond in {language}.

Previous context: {previous_context}

Current passage:
{current_passage}

Next preview: {next_preview}

Please answer with "Break point: <number>" and explain your choice.""",

    # Gisting (摘要) 提示：要求模型缩短段落，保留关键信息
    'gisting': """Please shorten the following passage while preserving key information.
Just provide the shortened version without explanation.

Please respond in {language}.

Passage:
{page_text}""",

    # 查找提示：要求模型根据摘要和查询，判断哪些页面最相关
    'lookup': """Based on the following document summary and user query, determine which pages would be most relevant to review.

Please respond in {language}.

Document Summary:
{gist_context}

User Query: {query}

Please respond with page numbers in format: [1, 3, 5] and explain your reasoning briefly.""",

    # 响应生成提示：要求模型根据上下文，全面回答用户查询
    'response': """Based on the following context from a long document, please provide a comprehensive answer to the user's query.

Please respond in {language}.

Context:
{context}

User Query: {query}

Instructions:
- Provide a detailed, informative response
- Structure your answer clearly with headings or bullet points when appropriate
- Reference the document content explicitly
- Ensure accuracy and avoid speculation beyond the context
- If context is insufficient, state this clearly

Answer:"""
}

# 语言代码到提示中语言名称的映射
LANGUAGE_MAPPING = {
    'zh': 'Chinese (中文)',
    'en': 'English'
}

# 用于文本切分的标点符号集
SENTENCE_ENDINGS = {
    'primary': ['。', '！', '？', '……'],           # 主要的句子结束符（强分割）
    'secondary': ['；', '：'],                     # 次要的句子结束符（中等分割）
    'english': ['.', '!', '?'],                   # 英文的句子结束符
    # 混合模式下所有可能的结束符
    'mixed': ['。', '！', '？', '；', '：', '……', '.', '!', '?', ';', ':']
}

# ==================== 2. 工具函数 (Utility Functions) ====================


def detect_language(text: str) -> str:
    """
    通过字符分析自动检测文本语言。

    Args:
        text: 需要分析的输入文本。

    Returns:
        'zh' 代表中文, 'en' 代表英文。
    """
    # 如果文本为空或只包含空白，默认为英文
    if not text.strip():
        return 'en'

    # 统计中文字符数量
    chinese_chars = len(re.findall(r'[\u4e00-\u9fff]', text))
    # 统计总字符数（去除空白符）
    total_chars = len(re.sub(r'\s', '', text))

    # 如果没有有效字符，默认为英文
    if total_chars == 0:
        return 'en'

    # 计算中文字符比例
    chinese_ratio = chinese_chars / total_chars

    # 如果中文字符比例超过10%，则判定为中文
    return 'zh' if chinese_ratio > 0.1 else 'en'


def count_text_units(text: str, language: str) -> int:
    """
    根据指定语言计算文本单位。

    Args:
        text: 输入文本。
        language: 语言代码, 'zh' 为中文, 'en' 为英文。

    Returns:
        文本单位的数量。
    """
    if language == 'zh':
        # 中文模式下采用混合单位计数法：
        # - 中文字 = 1 单位
        # - 英文单词 = 1 单位
        # - 数字 = 0.5 单位
        # - 标点和空格 = 0.1 单位
        units = 0
        i = 0

        while i < len(text):
            char = text[i]

            if '\u4e00' <= char <= '\u9fff':
                # 中文字符
                units += 1
                i += 1
            elif char.isalpha():
                # 英文单词（连续的字母数字组合）
                start_i = i
                while i < len(text) and (text[i].isalnum() or text[i] in ' -_'):
                    i += 1
                if i > start_i:  # 确保确实找到了一个单词
                    units += 1
            elif char.isdigit():
                # 数字序列
                while i < len(text) and (text[i].isdigit() or text[i] in '.'):
                    i += 1
                units += 0.5
            else:
                # 标点和空格
                units += 0.1
                i += 1

        return int(units)
    else:
        # 英文模式下，直接按空格切分计算单词数
        return len(text.split())


def get_text_statistics(text: str) -> Dict:
    """
    获取全面的文本统计信息。

    Args:
        text: 输入文本。

    Returns:
        包含文本统计信息（如语言、单位数、字符数等）的字典。
    """
    language = detect_language(text)

    return {
        'language': language,
        'total_units': count_text_units(text, language),
        'total_characters': len(text),
        'total_lines': len(text.split('\n')),
        'paragraphs': len([p for p in text.split('\n') if p.strip()]),
        'chinese_chars': len(re.findall(r'[\u4e00-\u9fff]', text)),
        'english_words': len(re.findall(r'[a-zA-Z]+', text))
    }


def is_sentence_ending(text: str, pos: int, language: str) -> bool:
    """
    判断在给定位置的标点符号是否代表句子结束。
    这是一个启发式规则，用于处理复杂的文本情况，特别是中英混合内容。

    Args:
        text: 完整文本。
        pos: 标点符号在文本中的位置。
        language: 文本语言。

    Returns:
        如果该标点是句子结束符，则返回 True。
    """
    # 如果是文本的最后一个字符，则视为句子结束
    if pos + 1 >= len(text):
        return True

    char = text[pos]
    next_char = text[pos + 1]

    # 针对英文句点（.）的特殊处理，以避免错误地切分缩写词
    if char == '.' and language == 'en':
        # 检查常见的缩写词，如 Mr., Mrs., Dr. 等
        if pos >= 2:
            prev_chars = text[max(0, pos-3):pos]
            common_abbrevs = ['Mr', 'Mrs', 'Dr',
                              'Prof', 'etc', 'vs', 'Inc', 'Ltd']
            for abbrev in common_abbrevs:
                if prev_chars.endswith(abbrev):
                    return False

        # 如果句点后跟空格和大写字母，很可能是句子结尾
        if next_char.isspace() and pos + 2 < len(text) and text[pos + 2].isupper():
            return True

        # 如果句点后跟中文字符，也视为句子结尾
        if '\u4e00' <= next_char <= '\u9fff':
            return True

    # 中文的句号、问号、感叹号等通常是可靠的句子结束符
    if char in ['。', '！', '？', '……']:
        return True

    # 次要结束符（如分号、冒号）后跟空格时，也视为结束
    if char in ['；', '：'] and next_char.isspace():
        return True

    return False


# ==================== 3. 本地模型接口 (Local Model Interface) ====================

def query_llm(
    prompt: str,
    api_url: str = None,
    model: str = None,
    temperature: float = 0.0,
    max_tokens: int = 4096,
    max_retries: int = 5,
    provider: str = None
) -> str:
    """
    查询大语言模型 API，支持多种供应商。

    Args:
        prompt: 发送给模型的输入提示。
        api_url: API 的终端地址。
        model: 要使用的模型名称。
        temperature: 控制生成文本随机性的温度参数。
        max_tokens: 生成响应的最大 token 数量。
        max_retries: 请求失败时的最大重试次数。
        provider: 指定使用的提供商 ('ollama', 'deepseek', 'kimi')。

    Returns:
        模型返回的文本响应。

    Raises:
        Exception: 在多次重试后仍然无法获取 API 响应时抛出。
    """
    # 确定使用的提供商
    if provider is None:
        provider = LLM_CONFIG.get('default_provider', 'ollama')

    # 获取提供商配置
    provider_config = LLM_CONFIG['providers'].get(provider)
    if not provider_config:
        raise ValueError(f"未找到提供商 '{provider}' 的配置")

    # 使用提供商配置中的参数，如果没有在函数参数中指定
    api_url = api_url or provider_config.get('api_url')
    model = model or provider_config.get('model')
    max_retries = max_retries or provider_config.get('max_retries', 3)
    max_tokens = max_tokens or provider_config.get('max_tokens', 4096)
    api_key = provider_config.get('api_key', '')

    # 设置请求头
    headers = {
        'Content-Type': 'application/json'
    }

    # 根据提供商设置认证信息
    if provider in ['deepseek', 'kimi'] and api_key:
        headers['Authorization'] = f'Bearer {api_key}'

    # 构建请求体
    payload = {}
    if provider == 'ollama':
        # Ollama API 格式
        payload = {
            'model': model,
            'messages': [{'role': 'user', 'content': prompt}],
            'stream': False,
            'options': {
                'temperature': temperature,
                'num_predict': max_tokens
            }
        }
    elif provider in ['deepseek', 'kimi']:
        # DeepSeek 和 Kimi API 格式 (OpenAI 兼容)
        payload = {
            'model': model,
            'messages': [{'role': 'user', 'content': prompt}],
            'temperature': temperature,
            'max_tokens': max_tokens
        }

    # 实现带重试逻辑的请求循环
    for attempt in range(max_retries):
        try:
            # 发送 POST 请求，设置较长的超时时间以应对模型生成耗时
            response = requests.post(
                api_url, headers=headers, json=payload, timeout=120)
            response.raise_for_status()  # 如果状态码不是 2xx，则抛出 HTTPError

            # 解析响应
            data = response.json()
            if provider == 'ollama':
                return data['message']['content']
            elif provider in ['deepseek', 'kimi']:
                return data['choices'][0]['message']['content']

        except requests.exceptions.Timeout:
            print(
                f'{datetime.datetime.now()}: 请求超时 (尝试 {attempt + 1}/{max_retries})')
            if attempt < max_retries - 1:
                time.sleep(10)  # 等待一段时间后重试
        except requests.exceptions.HTTPError as e:
            if e.response.status_code == 429:  # 429 状态码表示速率限制
                print(f'{datetime.datetime.now()}: 达到速率限制，等待...')
                time.sleep(30)
            else:
                print(f'{datetime.datetime.now()}: HTTP 错误: {e}')
                if attempt < max_retries - 1:
                    time.sleep(10)
        except Exception as e:
            print(f'{datetime.datetime.now()}: 发生意外错误: {e}')
            if attempt < max_retries - 1:
                time.sleep(10)

    # 如果所有重试都失败，则抛出异常
    raise Exception(f'查询 API 在 {max_retries} 次尝试后失败')


def get_prompt_with_language(template_name: str, language: str, **kwargs) -> str:
    """
    根据模板名称和语言，生成包含特定语言指令的提示。

    Args:
        template_name: 在 UNIVERSAL_PROMPT_TEMPLATES 中的模板名称。
        language: 目标语言代码 ('zh' 或 'en')。
        **kwargs: 用于填充模板的变量。

    Returns:
        格式化后的、包含语言指令的完整提示字符串。
    """
    template = UNIVERSAL_PROMPT_TEMPLATES[template_name]
    language_instruction = LANGUAGE_MAPPING.get(
        language, 'English')  # 如果语言不存在，默认为英语

    # 使用 format 方法填充模板中的 {language} 和其他变量
    return template.format(language=language_instruction, **kwargs)


# ==================== 4. 文本分割函数 (Text Segmentation Functions) ====================

# 定义一个从右引号/括号到左引号/括号的映射。
# 这是基于栈的解析方法的核心，用于正确处理嵌套结构。
QUOTE_MAP = {
    '”': '“', '’': '‘', '"': '"', "'": "'",
    '』': '『', '》': '《', ')': '(', '）': '（'
}

# 从 QUOTE_MAP 派生出开、闭引号集合，用于快速查找，提高效率。
OPENING_QUOTES = set(QUOTE_MAP.values())
CLOSING_QUOTES = set(QUOTE_MAP.keys())
# 定义可能同时用作开闭引号的模糊引号（例如英文中的 " 和 '）
AMBIGUOUS_QUOTES = {"\"", "'"}


def split_by_punctuation(text: str, language: str) -> List[str]:
    """
    使用基于栈的方法按标点符号切分文本，以稳健地处理嵌套和不匹配的引号/括号。

    这个函数是文本分割的核心，它能确保只在引号/括号外部的句子结束符处进行切分，
    避免了将引用的对话或专有名词错误地断开。
    """
    if not text.strip():
        return []

    segments = []
    current_segment = ""
    quote_stack = []  # 用于跟踪当前嵌套的引号

    i = 0
    while i < len(text):
        char = text[i]
        current_segment += char

        # --- 基于栈的引号处理逻辑 ---
        if char in OPENING_QUOTES:
            # 处理模糊引号：如果它与栈顶的引号相同，则视为闭合；否则视为开启。
            if char in AMBIGUOUS_QUOTES and quote_stack and quote_stack[-1] == char:
                quote_stack.pop()  # 弹出匹配的开引号
            else:
                quote_stack.append(char)  # 压入开引号
        elif char in CLOSING_QUOTES:
            # 如果遇到闭引号，且它与栈顶的开引号匹配，则弹出
            if quote_stack and quote_stack[-1] == QUOTE_MAP[char]:
                quote_stack.pop()

        # --- 切分逻辑 ---
        # 仅当不在任何引号内部 (quote_stack为空) 且当前字符是句子结束符时，才考虑切分
        if not quote_stack and char in SENTENCE_ENDINGS['mixed'] and is_sentence_ending(text, i, language):
            end_pos = i
            # 贪婪地消耗结尾的空格和紧随其后的闭引号，确保它们被包含在当前段落中
            while end_pos + 1 < len(text) and (text[end_pos + 1].isspace() or text[end_pos + 1] in CLOSING_QUOTES):
                end_pos += 1
                current_segment += text[end_pos]

            segments.append(current_segment.strip())
            current_segment = ""
            i = end_pos  # 更新主循环的索引

        i += 1

    # 将最后一个切分后剩余的文本作为一个段落添加
    if current_segment.strip():
        segments.append(current_segment.strip())

    # 过滤掉可能产生的空字符串
    return [seg for seg in segments if seg.strip()]


def split_text_by_paragraphs(text: str) -> List[str]:
    """
    按段落切分文本，特别处理了中文的全角空格缩进。

    Args:
        text: 输入文本。

    Returns:
        由段落组成的列表。
    """
    # 统一换行符为 \n
    text = re.sub(r'\r\n', '\n', text)

    # 首先按一个或多个空行进行切分
    paragraphs = re.split(r'\n\s*\n', text)

    # 进一步处理中文段首缩进（全角空格或制表符）
    result = []
    for para in paragraphs:
        para = para.strip()
        if para:
            # 使用正则表达式的零宽断言，按以缩进开始的行进行切分
            sub_paras = re.split(r'\n(?=\s*[　\t])', para)
            result.extend([p.strip() for p in sub_paras if p.strip()])

    return result


def parse_text(text: str, language: str) -> List[str]:
    """
    通用的文本解析函数，结合了按段落和按标点两种切分方式。

    Args:
        text: 原始输入文本。
        language: 文本语言。

    Returns:
        一个由细粒度文本片段组成的列表，可供后续处理。
    """
    # 1. 先按段落进行粗粒度切分
    paragraphs = split_text_by_paragraphs(text)

    # 2. 再对每个段落按标点进行细粒度切分
    segments = []
    for para in paragraphs:
        para_segments = split_by_punctuation(para, language)
        segments.extend(para_segments)

    return segments


# ==================== 5. 解析函数 (Parsing Functions) ====================

def clean_llm_response(response: str) -> str:
    """
    清理大语言模型的响应，移除"思考过程"的标签和其他不需要的构件。

    LLM 有时会在回答中包含 <thinking>...</thinking> 这样的标签来说明其推理过程，
    这个函数可以有效地将这些元信息剥离，只留下干净的最终答案。

    Args:
        response: 来自 LLM 的原始响应字符串。

    Returns:
        清理后的响应文本。
    """
    if not response:
        return ""

    # 使用正则表达式移除 <think>...</think> 等常见思考标签（不区分大小写，匹配多行）
    response = re.sub(r'<think>.*?</think>', '', response,
                      flags=re.DOTALL | re.IGNORECASE)
    response = re.sub(r'<thinking>.*?</thinking>', '',
                      response, flags=re.DOTALL | re.IGNORECASE)
    response = re.sub(r'<thought>.*?</thought>', '',
                      response, flags=re.DOTALL | re.IGNORECASE)
    response = re.sub(r'<reasoning>.*?</reasoning>', '',
                      response, flags=re.DOTALL | re.IGNORECASE)

    # 移除可能包含思考过程的 Markdown 代码块
    response = re.sub(r'```thinking.*?```', '', response,
                      flags=re.DOTALL | re.IGNORECASE)
    response = re.sub(r'```thought.*?```', '', response,
                      flags=re.DOTALL | re.IGNORECASE)

    # 移除一些常见的、表示思考过程的前缀
    thinking_prefixes = [
        r'^(好的，我现在需要.*?。)',
        r'^(Let me think about this.*?…)',
        r'^(I need to.*?…)',
        r'^(First, I.*?…)',
        r'^(Okay, I.*?…)',
        r'^(好，我.*?。)',
        r'^(首先，我.*?。)',
    ]

    for prefix in thinking_prefixes:
        response = re.sub(prefix, '', response,
                          flags=re.MULTILINE | re.IGNORECASE)

    # 清理多余的空白符和换行
    response = re.sub(r'\n\s*\n\s*\n', '\n\n', response)  # 将三个及以上的换行符合并为两个
    response = response.strip()  # 移除首尾空白

    return response


def parse_pause_point(response: str) -> Optional[int]:
    """
    从模型对分页任务的响应中解析出断点（pause point）的编号。

    Args:
        response: 模型的响应文本。

    Returns:
        解析出的断点编号（整数），如果未找到则返回 None。
    """
    # 首先清理响应文本
    response = clean_llm_response(response)

    try:
        # 优先匹配 "Break point: <number>" 这种最明确的格式
        match = re.search(r'Break point:\s*<(\d+)>', response, re.IGNORECASE)
        if match:
            return int(match.group(1))

        # 匹配中文格式 "断点: <number>"
        match = re.search(r'断点[:：]\s*<(\d+)>', response)
        if match:
            return int(match.group(1))

        # 匹配被尖括号包裹的数字 <number>
        match = re.search(r'<(\d+)>', response)
        if match:
            return int(match.group(1))

        # 如果上述格式都未匹配，则尝试一些更宽松的、基于关键词的模式
        patterns = [
            r'(?:break point|断点)[:：]?\s*(\d+)',
            r'(?:选择|choose|select)[:：]?\s*(\d+)',
            r'(?:标签|label|tag)[:：]?\s*(\d+)'
        ]

        for pattern in patterns:
            match = re.search(pattern, response, re.IGNORECASE)
            if match:
                return int(match.group(1))

        return None
    except (ValueError, AttributeError):
        # 如果在转换数字等过程中发生错误，安全地返回 None
        return None


def parse_page_ids(response: str) -> List[int]:
    """
    从模型对查找任务的响应中解析出相关的页面ID列表。

    Args:
        response: 模型的响应文本。

    Returns:
        一个包含相关页面ID（整数）的列表。
    """
    # 首先清理响应文本
    response = clean_llm_response(response)

    page_ids = []
    try:
        # 优先匹配 [1, 2, 3] 这样的 JSON 数组格式
        match = re.search(r'\[([^\]]+)\]', response)
        if match:
            ids_str = match.group(1).split(',')
            for id_str in ids_str:
                id_str = re.sub(r'[^\d]', '', id_str)  # 清理字符串，只保留数字
                if id_str.isdigit():
                    page_ids.append(int(id_str))

        # 如果未找到，尝试匹配中文格式，如 "第X页, 第Y页"
        if not page_ids:
            chinese_pages = re.findall(r'第(\d+)页', response)
            page_ids = [int(num) for num in chinese_pages]

        # 再次尝试匹配英文格式，如 "Page X, Page Y"
        if not page_ids:
            english_pages = re.findall(r'[Pp]age\s+(\d+)', response)
            page_ids = [int(num) for num in english_pages]

        # 作为最后的手段，如果以上格式都失败，则直接在文本中查找独立的数字
        if not page_ids:
            numbers = re.findall(r'\b(\d+)\b', response)
            # 对找到的数字进行过滤，排除可能是年份等无关数字
            valid_numbers = [int(num)
                             for num in numbers if 0 <= int(num) <= 50]  # 假设页码不会超过50
            page_ids = valid_numbers[:5]  # 最多取前5个有效数字

    except (ValueError, AttributeError):
        pass  # 解析失败时，静默处理，返回空列表

    return page_ids


# ==================== 6. 核心处理函数 (Core Processing Functions) ====================

def create_page(
    segments: List[str],
    start_idx: int,
    config: Dict,
    language: str
) -> Tuple[List[str], int]:
    """
    从文本片段列表中创建单个页面。

    该函数会根据配置中的单位限制，从指定的起始位置开始，不断聚合文本片段，
    直到达到阈值。然后，它会调用 LLM 来决定一个自然的断点，以保证页面的连贯性。

    Args:
        segments: 所有文本片段的列表。
        start_idx: 本次创建页面开始的片段索引。
        config: 包含 `unit_limit` 和 `start_threshold` 等参数的配置字典。
        language: 文本语言。

    Returns:
        一个元组，包含 (当前页面的片段列表, 下一个页面开始的片段索引)。
    """
    unit_limit = config.get('unit_limit', 1000)
    start_threshold = config.get('start_threshold', 300)

    page_segments_with_markers = [segments[start_idx]]
    unit_count = count_text_units(segments[start_idx], language)
    j = start_idx + 1

    # 循环添加片段，直到达到单位上限
    while unit_count < unit_limit and j < len(segments):
        segment_units = count_text_units(segments[j], language)
        unit_count += segment_units

        # 当内容超过寻找断点的阈值时，插入一个带编号的断点标记
        if unit_count >= start_threshold:
            page_segments_with_markers.append(f"<{j}>")  # 例如: <15>

        page_segments_with_markers.append(segments[j])
        j += 1

    page_segments_with_markers.append(f"<{j}>")  # 在末尾也添加一个标记

    # 如果页面太短，直接返回，不调用 LLM
    min_units = 100 if language == 'zh' else 50
    if unit_count < min_units:
        return segments[start_idx:j], j

    # 调用 LLM 来决定最佳断点
    try:
        # 准备上下文信息，帮助 LLM 做出更好的判断
        preceding_context = "" if start_idx == 0 else "...\n" + \
            '\n'.join(segments[max(0, start_idx-2):start_idx])
        next_preview = "" if j >= len(segments) else segments[j] + "\n..."

        # 生成分页提示
        prompt = get_prompt_with_language(
            'pagination',
            language,
            previous_context=preceding_context,
            current_passage='\n'.join(page_segments_with_markers),
            next_preview=next_preview
        )

        response = query_llm(
            prompt,
            temperature=config.get('temperature', 0.0),
            provider=config.get('provider', LLM_CONFIG.get(
                'default_provider', 'ollama'))
        )

        # 解析模型返回的断点
        cleaned_response = clean_llm_response(response)
        pause_point = parse_pause_point(cleaned_response)

        # 如果模型返回了一个有效的、在当前范围内的断点，则使用该断点进行切分
        if pause_point and start_idx < pause_point <= j:
            return segments[start_idx: pause_point], pause_point
        else:
            # 如果模型没有返回有效断点，或选择了最后一个，则退回到基于长度的默认切分
            return segments[start_idx: j], j

    except Exception as e:
        if config.get('verbose', False):
            print(f"分页决策失败，使用默认切分: {e}")
        return segments[start_idx: j], j


def paginate_text(text: str, config: Dict, language: str) -> List[List[str]]:
    """
    对长文本进行分页，将其切分为多个页面。

    Args:
        text: 完整的输入文本。
        config: 配置字典。
        language: 文本语言。

    Returns:
        一个页面列表，其中每个页面是其包含的文本片段的列表。
    """
    # 1. 将原始文本解析成细粒度的片段
    segments = parse_text(text, language)
    total_segments = len(segments)

    pages = []
    i = 0

    if config.get('verbose', False):
        print(
            f"[分页] 开始为 {total_segments} 个片段进行分页...")

    # 2. 循环创建页面，直到所有片段都被处理
    while i < total_segments:
        if config.get('verbose', False):
            progress = (i / total_segments) * 100 if total_segments > 0 else 0
            print(
                f"\n[分页] 创建页面 {len(pages) + 1}，起始片段 {i}/{total_segments} ({progress:.1f}%)")

        page_segments, next_i = create_page(
            segments, i, config, language)
        pages.append(page_segments)

        if config.get('verbose', False):
            header = f" 页面 {len(pages)} 内容 "
            print("-" * 25 + header + "-" * 25)
            print('\n'.join(page_segments))
            print("-" * (50 + len(header)))

        i = next_i  # 更新下一个页面的起始索引

    if config.get('verbose', False):
        print(
            f"\n[分页] 完成: 为 {language} 文本创建了 {len(pages)} 个页面")

    return pages


def _generate_single_gist(page_tuple: tuple) -> tuple[int, str]:
    """为单个页面生成摘要，并返回索引和结果"""
    i, page, config, language = page_tuple
    page_text = '\n'.join(page)
    try:
        prompt = get_prompt_with_language('gisting', language, page_text=page_text)
        response = query_llm(
            prompt,
            temperature=config.get('temperature', 0.0),
            provider=config.get('provider', LLM_CONFIG.get('default_provider', 'ollama'))
        )
        gist_memory = clean_llm_response(response)
        if not gist_memory or len(gist_memory.strip()) < 10:
            raise ValueError("生成的摘要过短或为空")
        return i, gist_memory
    except Exception as e:
        # 保持现有的回退逻辑
        fallback_length = 200 if language == 'zh' else 100
        gist_memory = page_text[:fallback_length] + "..." if len(page_text) > fallback_length else page_text
        return i, gist_memory


def generate_gist_memories(

    pages: List[List[str]],
    config: Dict,
    language: str
) -> List[str]:
    """
    为所有页面生成摘要记忆 (Gist Memory)。

    Args:
        pages: 页面列表。
        config: 配置字典。
        language: 文本语言。

    Returns:
        一个包含每个页面摘要的列表。
    """
    gist_memories = [None] * len(pages)

    tasks = []
    for i, page in enumerate(pages):
        tasks.append((i, page, config, language))

    with ThreadPoolExecutor(max_workers=5) as executor:
        future_to_index = {executor.submit(_generate_single_gist, task): task[0] for task in tasks}
        
        for future in as_completed(future_to_index):
            index, gist = future.result()
            gist_memories[index] = gist
            
            if config.get('verbose', False):
                preview = gist[:100] + "..." if len(gist) > 100 else gist
                print(f"[摘要] 页面 {index}: {preview}")

    return gist_memories



def build_gist_context(gist_memories: List[str]) -> str:
    """
    构建用于查找阶段的摘要记忆上下文。

    Args:
        gist_memories: 摘要记忆列表。

    Returns:
        一个格式化的字符串，其中包含所有带页码的摘要。
    """
    gist_context = []
    for i, gist in enumerate(gist_memories):
        gist_context.append(f"<Page {i}>\n{gist}")
    return '\n\n'.join(gist_context)


def find_relevant_pages(
    gist_memories: List[str],
    query: str,
    config: Dict,
    language: str
) -> List[int]:
    """
    根据用户查询和摘要记忆，找到最相关的页面。

    Args:
        gist_memories: 摘要记忆列表。
        query: 用户查询。
        config: 配置字典。
        language: 文本语言。

    Returns:
        一个包含相关页面索引的列表。
    """
    # 1. 构建摘要上下文
    gist_context = build_gist_context(gist_memories)

    try:
        # 2. 生成 lookup 提示
        prompt = get_prompt_with_language(
            'lookup',
            language,
            gist_context=gist_context,
            query=query
        )

        # 3. 查询模型
        response = query_llm(
            prompt,
            temperature=config.get('temperature', 0.0),
            provider=config.get('provider', LLM_CONFIG.get(
                'default_provider', 'ollama'))
        )

        # 4. 解析并验证返回的页面ID
        cleaned_response = clean_llm_response(response)
        page_ids = parse_page_ids(cleaned_response)

        valid_page_ids = []
        for page_id in page_ids:
            if 0 <= page_id < len(gist_memories):
                valid_page_ids.append(page_id)

        # 5. 限制返回的页面数量
        max_pages = config.get('max_lookup_pages', 5)
        valid_page_ids = valid_page_ids[:max_pages]

        if config.get('verbose', False):
            print(f"[查找] 选择的相关页面: {valid_page_ids}")

        return valid_page_ids

    except Exception as e:
        if config.get('verbose', False):
            print(f"页面查找失败，使用前几页作为备用: {e}")
        # 备用策略：如果查找失败，返回前几页
        max_pages = min(config.get('max_lookup_pages', 5), len(gist_memories))
        return list(range(max_pages))


def build_expanded_context(
    gist_memories: List[str],
    pages: List[List[str]],
    relevant_page_ids: List[int]
) -> str:
    """
    为最终的响应生成构建扩展上下文。

    这个上下文包括了所有页面的摘要（作为全局概览）和被选中的相关页面的详细原文。

    Args:
        gist_memories: 所有页面的摘要记忆。
        pages: 所有页面的原文片段。
        relevant_page_ids: 被认定为相关的页面的索引列表。

    Returns:
        一个包含摘要和详细原文的、准备好输入给模型的长字符串。
    """
    context_parts = []

    # 首先添加所有页面的摘要作为文档的整体背景
    gist_context = build_gist_context(gist_memories)
    context_parts.append("文档摘要总览:")
    context_parts.append(gist_context)
    context_parts.append("\n相关页面的详细内容:")

    # 然后添加相关页面的详细原文
    for page_id in relevant_page_ids:
        if 0 <= page_id < len(pages):
            page_content = '\n'.join(pages[page_id])
            context_parts.append(f"\n--- 页面 {page_id} (详细内容) ---")
            context_parts.append(page_content)

    return '\n'.join(context_parts)


def generate_response(
    context: str,
    query: str,
    config: Dict,
    language: str
) -> str:
    """
    根据最终的扩展上下文和用户查询生成回答。

    Args:
        context: 扩展上下文（包含摘要和相关原文）。
        query: 用户查询。
        config: 配置字典。
        language: 文本语言。

    Returns:
        模型生成的最终回答。
    """
    try:
        # 生成 response 提示
        prompt = get_prompt_with_language(
            'response',
            language,
            context=context,
            query=query
        )

        response = query_llm(
            prompt,
            temperature=config.get('temperature', 0.0),
            provider=config.get('provider', LLM_CONFIG.get(
                'default_provider', 'ollama'))
        )

        # 清理响应并返回
        cleaned_response = clean_llm_response(response)
        return cleaned_response

    except Exception as e:
        if config.get('verbose', False):
            print(f"响应生成失败: {e}")
        return f"抱歉，在处理您的查询时遇到错误: {str(e)}"


# ==================== 7. 主要接口函数 (Main Interface Functions) ====================

def process_long_text(
    text: str,
    query: str,
    language: str = 'auto',
    verbose: bool = False,
    save_path: str = None,
    load_processed: str = None,
    **kwargs
) -> Dict:
    """
    通用的长文本处理接口，接收文本字符串作为输入。

    Args:
        text: 需要处理的输入文本字符串。
        query: 用户的查询。
        language: 语言代码 ('auto', 'zh', 'en')。'auto' 会自动检测。
        verbose: 是否启用详细输出模式，打印处理过程中的详细信息。
        save_path: 保存处理结果（包括页面、摘要、最终响应等）的文件路径（不含扩展名）。
        load_processed: 从已保存的 pkl 文件加载预处理好的页面和摘要，以跳过分页和摘要步骤。
        **kwargs: 其他可以覆盖 [DEFAULT_CONFIG]的配置参数。

    Returns:
        一个包含所有处理结果的字典。
    """

    # 如果语言设置为 'auto'，则自动检测
    if language == 'auto':
        language = detect_language(text)

    # 根据检测到的语言获取配置，并用 kwargs 中的参数更新
    config = DEFAULT_CONFIG[language].copy()
    config.update(kwargs)
    config['verbose'] = verbose

    if verbose:
        print(
            f"[处理] 开始处理 {language} 文本，单位数: {count_text_units(text, language)}")

    # 尝试从文件加载预处理好的文档（页面和摘要）
    pages = None
    gist_memories = None

    if load_processed and os.path.exists(load_processed):
        try:
            _, pages, gist_memories = load_processed_document(load_processed)
            if verbose:
                print(
                    f"[加载] 成功从 {load_processed} 加载预处理文档")
        except Exception as e:
            if verbose:
                print(f"[加载] 加载预处理文档失败: {e}")

    # 如果没有加载成功，则执行完整的文本处理流程
    if pages is None or gist_memories is None:
        if verbose:
            print("[处理] 未加载缓存，开始执行完整文本处理流程...")

        # 步骤 1: 文本分页
        pages = paginate_text(text, config, language)

        # 步骤 2: 生成摘要记忆
        gist_memories = generate_gist_memories(
            pages, config, language)

        # 如果指定了保存路径，则保存处理好的文档
        if save_path:
            try:
                save_processed_document(
                    text, pages, gist_memories, f"{save_path}_processed.pkl")
                if verbose:
                    print(
                        f"[保存] 已将预处理文档保存至 {save_path}_processed.pkl")
            except Exception as e:
                if verbose:
                    print(f"[保存] 保存预处理文档失败: {e}")

    # 步骤 3: 根据查询查找相关页面
    relevant_pages = find_relevant_pages(
        gist_memories, query, config, language)

    # 步骤 4: 构建用于生成回答的扩展上下文
    expanded_context = build_expanded_context(
        gist_memories, pages, relevant_pages)

    # 步骤 5: 生成最终回答
    response = generate_response(
        expanded_context, query, config, language)

    # 计算压缩率
    original_units = count_text_units(text, language)
    gist_units = sum(count_text_units(gist, language)
                     for gist in gist_memories)
    compression_rate = 1.0 - \
        (gist_units / original_units) if original_units > 0 else 0.0

    # 构建最终返回的结果字典
    result = {
        'pages': pages,
        'gist_memories': gist_memories,
        'relevant_pages': relevant_pages,
        'expanded_context': expanded_context,
        'response': response,
        'compression_rate': compression_rate,
        'language': language,
        'statistics': get_text_statistics(text)
    }

    # 如果指定了保存路径，则保存完整的处理结果
    if save_path:
        try:
            save_result(result, f"{save_path}_result.json")
            if verbose:
                print(f"[保存] 已将完整结果保存至 {save_path}_result.json")
        except Exception as e:
            if verbose:
                print(f"[保存] 保存完整结果失败: {e}")

    return result


def process_text_file(
    file_path: str,
    query: str,
    language: str = 'auto',
    force_reprocess: bool = False,
    verbose: bool = False,
    **kwargs
) -> Dict:
    """
    处理文本文件的主要接口，内置了自动缓存管理机制。

    该函数会首先检查是否存在与文本文件匹配的有效缓存。如果存在，则直接加载缓存，
    跳过耗时的分页和摘要步骤；否则，它会完整处理文本，并自动创建缓存以备将来使用。

    Args:
        file_path: 文本文件的路径。
        query: 用户的查询。
        language: 语言设置 ('auto', 'zh', 'en')。
        force_reprocess: 如果为 True，则强制重新处理文本，忽略现有缓存。
        verbose: 是否启用详细输出模式。
        **kwargs: 其他配置参数。

    Returns:
        一个包含处理结果和缓存信息的字典。
    """

    # 检查文件是否存在
    if not os.path.exists(file_path):
        raise FileNotFoundError(f"找不到文本文件: {file_path}")

    # 生成对应的 pkl 缓存文件路径
    pkl_path = get_cache_path(file_path)

    # 检查是否需要重新处理（强制、缓存不存在或缓存无效）
    need_reprocess = force_reprocess or not validate_cache(
        file_path, pkl_path)

    if need_reprocess:
        if verbose:
            cache_info = get_cache_info(file_path)
            if cache_info['status'] == 'no_cache':
                print(f"[处理] 未找到缓存，开始处理 {file_path}...")
            elif cache_info['status'] == 'invalid':
                print(
                    f"[处理] 缓存无效 (文本已修改)，重新处理 {file_path}...")
            elif cache_info['status'] == 'corrupted':
                print(
                    f"[处理] 缓存损坏，重新处理 {file_path}...")
            elif force_reprocess:
                print(
                    f"[处理] 已启用强制重新处理，开始处理 {file_path}...")

        # 读取文本文件内容
        text = load_text_file(file_path)

        # 自动检测语言
        if language == 'auto':
            language = detect_language(text)

        # 获取配置
        config = DEFAULT_CONFIG[language].copy()
        config.update(kwargs)
        config['verbose'] = verbose

        if verbose:
            print(f"[处理] 检测到语言: {language}")
            print(f"[处理] 文本长度: {len(text)} 字符")

        # 执行完整处理流程
        pages = paginate_text(text, config, language)
        gist_memories = generate_gist_memories(
            pages, config, language)

        # 将处理结果保存到缓存
        save_cache_auto(
            file_path, text, pages, gist_memories, language, verbose)

    else:
        if verbose:
            print(f"[缓存] 从 {pkl_path} 加载缓存结果")

        # 加载有效的缓存结果
        text, pages, gist_memories, language = load_cache_auto(
            pkl_path)

        # 获取配置
        config = DEFAULT_CONFIG[language].copy()
        config.update(kwargs)
        config['verbose'] = verbose

    # --- 从这里开始，无论是加载缓存还是新处理，流程都一样 ---
    if verbose:
        print(f"[查询] 正在处理查询: {query}")
        print(
            f"[查询] 使用 {len(pages)} 个页面, {len(gist_memories)} 个摘要记忆")

    # 查找相关页面
    relevant_pages = find_relevant_pages(
        gist_memories, query, config, language)

    # 构建扩展上下文
    expanded_context = build_expanded_context(
        gist_memories, pages, relevant_pages)

    # 生成响应
    response = generate_response(
        expanded_context, query, config, language)

    # 计算压缩率
    original_units = count_text_units(text, language)
    gist_units = sum(count_text_units(gist, language)
                     for gist in gist_memories)
    compression_rate = 1.0 - \
        (gist_units / original_units) if original_units > 0 else 0.0

    # 构建最终结果
    result = {
        'pages': pages,
        'gist_memories': gist_memories,
        'relevant_pages': relevant_pages,
        'expanded_context': expanded_context,
        'response': response,
        'compression_rate': compression_rate,
        'language': language,
        'statistics': get_text_statistics(text),
        'cache_used': not need_reprocess,  # 标记本次操作是否使用了缓存
        'cache_path': pkl_path
    }

    if verbose:
        print(f"[结果] 压缩率: {compression_rate:.2%}")
        print(f"[结果] 是否使用缓存: {not need_reprocess}")
        print(f"[结果] 响应长度: {len(response)} 字符")

    return result


# ==================== 8. 缓存管理函数 (Cache Management Functions) ====================

def get_cache_path(file_path: str) -> str:
    """
    根据原始文本文件路径生成其对应的缓存文件路径。

    Args:
        file_path: 原始文本文件的路径。

    Returns:
        对应的 .pkl 缓存文件路径。
    """
    base_path = os.path.splitext(file_path)[0]  # 获取不带扩展名的文件名
    return f"{base_path}.pkl"


def validate_cache(txt_path: str, pkl_path: str) -> bool:
    """
    验证 pkl 缓存文件是否有效，并且与当前的 txt 文件内容匹配。

    验证标准包括：
    1. 缓存文件是否存在。
    2. 缓存文件是否能被成功加载。
    3. 缓存中是否包含所有必需的字段。
    4. 缓存中存储的原始文本哈希值是否与当前文本文件的哈希值一致。
    5. 缓存中的页面和摘要列表不能为空。
    6. 缓存版本是否与当前代码兼容。

    Args:
        txt_path: 原始文本文件的路径。
        pkl_path: 缓存文件的路径。

    Returns:
        如果缓存有效，返回 True，否则返回 False。
    """
    if not os.path.exists(pkl_path):
        return False

    try:
        # 检查 pkl 文件的完整性和内容
        with open(pkl_path, 'rb') as f:
            data = pickle.load(f)

        # 验证必要字段是否存在
        required_fields = ['original_text', 'pages',
                           'gist_memories', 'text_hash', 'version']
        if not all(field in data for field in required_fields):
            return False

        # 验证文本内容的哈希值是否匹配
        if not os.path.exists(txt_path):
            return False

        with open(txt_path, 'r', encoding='utf-8') as f:
            current_text = f.read()

        current_hash = hash(current_text)
        stored_hash = data.get('text_hash')

        if current_hash != stored_hash:
            return False

        # 验证处理结果是否为空
        if not data['pages'] or not data['gist_memories']:
            return False

        # 验证版本兼容性
        if not check_cache_version(data):
            return False

        return True

    except Exception as e:
        # 任何异常（如 pickle 加载失败）都意味着缓存无效
        return False


def check_cache_version(data: dict) -> bool:
    """
    检查缓存数据的版本是否与当前系统兼容。

    Args:
        data: 从缓存文件加载的数据字典。

    Returns:
        如果版本兼容，返回 True，否则返回 False。
    """
    cache_version = data.get('version', '1.0.0')
    current_version = "1.1.0"  # 定义当前代码的版本

    try:
        # 简单的版本兼容性检查：只比较主版本号
        cache_major = int(cache_version.split('.')[0])
        current_major = int(current_version.split('.')[0])

        return cache_major == current_major

    except (ValueError, AttributeError):
        return False


def load_text_file(file_path: str) -> str:
    """
    加载文本文件，并尝试多种编码格式以提高兼容性。

    Args:
        file_path: 文件路径。

    Returns:
        文件的内容字符串。
    """
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            return f.read()
    except UnicodeDecodeError:
        # 如果 utf-8 解码失败，尝试 gbk
        try:
            with open(file_path, 'r', encoding='gbk') as f:
                return f.read()
        except UnicodeDecodeError:
            # 如果 gbk 也失败，尝试 latin-1 作为最后的手段
            with open(file_path, 'r', encoding='latin-1') as f:
                return f.read()


def save_cache_auto(
    file_path: str,
    text: str,
    pages: List[List[str]],
    gist_memories: List[str],
    language: str,
    verbose: bool = False
) -> None:
    """
    自动将处理结果保存到对应的 .pkl 缓存文件中。

    Args:
        file_path: 原始文本文件的路径。
        text: 原始文本内容。
        pages: 处理后的页面列表。
        gist_memories: 摘要记忆列表。
        language: 检测到的语言。
        verbose: 是否显示详细信息。
    """
    pkl_path = get_cache_path(file_path)

    try:
        processed_doc = {
            'original_text': text,
            'pages': pages,
            'gist_memories': gist_memories,
            'text_hash': hash(text),  # 存储文本哈希以供验证
            'processed_time': datetime.datetime.now().isoformat(),
            'language': language,
            'statistics': get_text_statistics(text),
            'version': '1.1.0'  # 标记当前版本
        }

        with open(pkl_path, 'wb') as f:
            pickle.dump(processed_doc, f)

        if verbose:
            print(f"[缓存] 已将处理结果保存至 {pkl_path}")

    except Exception as e:
        if verbose:
            print(f"[缓存] 保存缓存失败: {e}")


def load_cache_auto(pkl_path: str) -> Tuple[str, List[List[str]], List[str], str]:
    """
    从指定的 .pkl 文件中自动加载缓存的处理结果。

    Args:
        pkl_path: 缓存文件的路径。

    Returns:
        一个元组，包含 (原始文本, 页面列表, 摘要列表, 语言)。
    """
    with open(pkl_path, 'rb') as f:
        doc = pickle.load(f)

    return (
        doc['original_text'],
        doc['pages'],
        doc['gist_memories'],
        doc['language']
    )


def get_cache_info(file_path: str) -> Dict:
    """
    获取指定文本文件对应的缓存文件的状态和信息。

    Args:
        file_path: 原始文本文件的路径。

    Returns:
        一个包含缓存状态（如 'valid', 'invalid', 'no_cache'）和元数据的字典。
    """
    pkl_path = get_cache_path(file_path)

    if not os.path.exists(pkl_path):
        return {"status": "no_cache", "path": pkl_path}

    try:
        with open(pkl_path, 'rb') as f:
            data = pickle.load(f)

        is_valid = validate_cache(file_path, pkl_path)

        return {
            "status": "valid" if is_valid else "invalid",
            "path": pkl_path,
            "created_time": data.get('processed_time'),
            "version": data.get('version'),
            "pages": len(data.get('pages', [])),
            "language": data.get('language'),
            "file_size": os.path.getsize(pkl_path)
        }

    except Exception as e:
        return {"status": "corrupted", "path": pkl_path, "error": str(e)}


def clear_cache(file_path: str = None, verbose: bool = False) -> None:
    """
    清理缓存文件。

    Args:
        file_path: 如果提供，则只清理该文件对应的缓存。如果为 None，则清理当前目录下所有的 .pkl 缓存文件。
        verbose: 是否显示详细信息。
    """
    if file_path:
        # 清理指定文件的缓存
        pkl_path = get_cache_path(file_path)
        if os.path.exists(pkl_path):
            os.remove(pkl_path)
            if verbose:
                print(f"[缓存] 已移除 {pkl_path}")
    else:
        # 清理当前目录下所有的 .pkl 缓存文件
        removed_count = 0
        for file in os.listdir('.'):
            if file.endswith('.pkl'):
                try:
                    os.remove(file)
                    removed_count += 1
                    if verbose:
                        print(f"[缓存] 已移除 {file}")
                except Exception as e:
                    if verbose:
                        print(f"[缓存] 移除 {file} 失败: {e}")

        if verbose:
            print(f"[缓存] 共移除了 {removed_count} 个缓存文件")


# ==================== 9. 持久化函数 (Persistence Functions) ====================

def save_result(result: Dict, filepath: str, format: str = 'json') -> None:
    """
    将处理结果保存到文件。

    Args:
        result: 包含处理结果的字典。
        filepath: 保存文件的路径。
        format: 保存的格式，支持 'json' 或 'pickle'。
    """
    result_copy = result.copy()
    result_copy['timestamp'] = datetime.datetime.now().isoformat()

    if format == 'json':
        # 为 JSON 序列化做准备
        json_result = result_copy.copy()
        # 为避免 JSON 文件过大，对其中可能很长的字段进行截断
        if 'expanded_context' in json_result and len(json_result['expanded_context']) > 10000:
            json_result['expanded_context'] = json_result['expanded_context'][:10000] + \
                "...[已截断]"

        with open(filepath, 'w', encoding='utf-8') as f:
            json.dump(json_result, f, indent=2, ensure_ascii=False)
    elif format == 'pickle':
        with open(filepath, 'wb') as f:
            pickle.dump(result_copy, f)
    else:
        raise ValueError("格式必须是 'json' 或 'pickle'")


def load_result(filepath: str, format: str = 'json') -> Dict:
    """
    从文件加载已保存的处理结果。

    Args:
        filepath: 文件路径。
        format: 文件格式，支持 'json' 或 'pickle'。

    Returns:
        加载的结果字典。
    """
    if format == 'json':
        with open(filepath, 'r', encoding='utf-8') as f:
            return json.load(f)
    elif format == 'pickle':
        with open(filepath, 'rb') as f:
            return pickle.load(f)
    else:
        raise ValueError("格式必须是 'json' 或 'pickle'")


def save_processed_document(
    text: str,
    pages: List[List[str]],
    gists: List[str],
    filepath: str
) -> None:
    """
    将预处理过的文档（原文、页面、摘要）保存起来，以便重用。

    Args:
        text: 原始文本。
        pages: 处理后的页面列表。
        gists: 摘要记忆列表。
        filepath: 保存路径 (通常是 .pkl 文件)。
    """
    processed_doc = {
        'original_text': text,
        'pages': pages,
        'gist_memories': gists,
        'text_hash': hash(text),
        'processed_time': datetime.datetime.now().isoformat(),
        'language': detect_language(text),
        'statistics': get_text_statistics(text)
    }

    with open(filepath, 'wb') as f:
        pickle.dump(processed_doc, f)


def load_processed_document(filepath: str) -> Tuple[str, List[List[str]], List[str]]:
    """
    加载预处理过的文档。

    Args:
        filepath: 文件路径。

    Returns:
        一个元组，包含 (原始文本, 页面列表, 摘要记忆列表)。
    """
    with open(filepath, 'rb') as f:
        doc = pickle.load(f)
    return doc['original_text'], doc['pages'], doc['gist_memories']
