#!/usr/bin/env python3
"""
Test script for caching functionality

This script tests the new file-based caching system with automatic
cache management and validation.
"""

import sys
import os
import time
from datetime import datetime

# Add current directory to path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

try:
    import read_agent_universal as ra
    print("✓ Read Agent Universal module imported successfully")
except ImportError as e:
    print(f"✗ Failed to import Read Agent Universal: {e}")
    sys.exit(1)

def test_cache_path_generation():
    """Test cache path generation"""
    print("\n" + "="*60)
    print("Cache Path Generation Tests")
    print("="*60)
    
    test_cases = [
        ("狂人日记.txt", "狂人日记.pkl"),
        ("document.txt", "document.pkl"),
        ("/path/to/file.txt", "/path/to/file.pkl"),
        ("file_without_extension", "file_without_extension.pkl"),
        ("file.with.dots.txt", "file.with.dots.pkl")
    ]
    
    for input_path, expected in test_cases:
        result = ra.get_cache_path(input_path)
        status = "✓" if result == expected else "✗"
        print(f"{status} '{input_path}' -> '{result}' (expected: '{expected}')")

def test_cache_info():
    """Test cache information retrieval"""
    print("\n" + "="*60)
    print("Cache Information Tests")
    print("="*60)
    
    # Test with existing file
    test_file = "狂人日记.txt"
    if os.path.exists(test_file):
        info = ra.get_cache_info(test_file)
        print(f"Cache info for {test_file}:")
        print(f"  Status: {info['status']}")
        print(f"  Path: {info['path']}")
        
        if info['status'] != 'no_cache':
            print(f"  Created: {info.get('created_time', 'Unknown')}")
            print(f"  Version: {info.get('version', 'Unknown')}")
            print(f"  Pages: {info.get('pages', 'Unknown')}")
            print(f"  Language: {info.get('language', 'Unknown')}")
            print(f"  File size: {info.get('file_size', 0)} bytes")
    else:
        print(f"✗ Test file {test_file} not found")
    
    # Test with non-existent file
    fake_file = "non_existent_file.txt"
    info = ra.get_cache_info(fake_file)
    print(f"\nCache info for {fake_file}:")
    print(f"  Status: {info['status']}")

def test_basic_caching():
    """Test basic caching functionality"""
    print("\n" + "="*60)
    print("Basic Caching Tests")
    print("="*60)
    
    test_file = "狂人日记.txt"
    
    if not os.path.exists(test_file):
        print(f"✗ Test file {test_file} not found")
        return
    
    # Clear any existing cache
    ra.clear_cache(test_file, verbose=True)
    
    # Test queries
    queries = [
        "这个故事的主人公是谁？",
        "What is the main theme of this story?"
    ]
    
    for i, query in enumerate(queries, 1):
        print(f"\n--- Query {i}: {query} ---")
        
        start_time = time.time()
        
        try:
            result = ra.process_text_file(
                file_path=test_file,
                query=query,
                verbose=True
            )
            
            end_time = time.time()
            processing_time = end_time - start_time
            
            print(f"✓ Processing completed in {processing_time:.2f} seconds")
            print(f"✓ Cache used: {result['cache_used']}")
            print(f"✓ Language: {result['language']}")
            print(f"✓ Pages: {len(result['pages'])}")
            print(f"✓ Compression rate: {result['compression_rate']:.2%}")
            print(f"✓ Response length: {len(result['response'])} characters")
            
            # Show response preview
            response_preview = result['response'][:200] + "..." if len(result['response']) > 200 else result['response']
            print(f"Response preview: {response_preview}")
            
        except Exception as e:
            print(f"✗ Processing failed: {e}")

def test_cache_validation():
    """Test cache validation functionality"""
    print("\n" + "="*60)
    print("Cache Validation Tests")
    print("="*60)
    
    test_file = "狂人日记.txt"
    
    if not os.path.exists(test_file):
        print(f"✗ Test file {test_file} not found")
        return
    
    pkl_path = ra.get_cache_path(test_file)
    
    # Test 1: Valid cache
    if os.path.exists(pkl_path):
        is_valid = ra.validate_processed_file(test_file, pkl_path)
        print(f"✓ Cache validation result: {is_valid}")
    else:
        print("No cache file exists for validation test")
    
    # Test 2: Non-existent cache
    fake_pkl = "fake_file.pkl"
    is_valid = ra.validate_processed_file(test_file, fake_pkl)
    print(f"✓ Non-existent cache validation: {is_valid} (should be False)")
    
    # Test 3: Cache info after processing
    info = ra.get_cache_info(test_file)
    print(f"✓ Cache status after processing: {info['status']}")

def test_force_reprocess():
    """Test force reprocessing functionality"""
    print("\n" + "="*60)
    print("Force Reprocess Tests")
    print("="*60)
    
    test_file = "狂人日记.txt"
    
    if not os.path.exists(test_file):
        print(f"✗ Test file {test_file} not found")
        return
    
    query = "测试强制重新处理功能"
    
    # First, ensure we have a cache
    print("--- Creating initial cache ---")
    result1 = ra.process_text_file(test_file, query, verbose=True)
    print(f"First run - Cache used: {result1['cache_used']}")
    
    # Then test normal processing (should use cache)
    print("\n--- Normal processing (should use cache) ---")
    result2 = ra.process_text_file(test_file, query, verbose=True)
    print(f"Second run - Cache used: {result2['cache_used']}")
    
    # Finally test force reprocess
    print("\n--- Force reprocess (should ignore cache) ---")
    result3 = ra.process_text_file(test_file, query, force_reprocess=True, verbose=True)
    print(f"Third run - Cache used: {result3['cache_used']}")

def test_multiple_queries_performance():
    """Test performance with multiple queries"""
    print("\n" + "="*60)
    print("Multiple Queries Performance Test")
    print("="*60)
    
    test_file = "狂人日记.txt"
    
    if not os.path.exists(test_file):
        print(f"✗ Test file {test_file} not found")
        return
    
    queries = [
        "这个故事的主人公是谁？",
        "故事的主题是什么？",
        "反映了什么社会问题？",
        "作者想表达什么观点？"
    ]
    
    # Clear cache first
    ra.clear_cache(test_file, verbose=True)
    
    total_time = 0
    results = []
    
    for i, query in enumerate(queries, 1):
        print(f"\n--- Query {i}/{len(queries)}: {query[:30]}... ---")
        
        start_time = time.time()
        
        try:
            result = ra.process_text_file(test_file, query, verbose=False)
            end_time = time.time()
            processing_time = end_time - start_time
            total_time += processing_time
            
            results.append({
                'query': query,
                'time': processing_time,
                'cache_used': result['cache_used'],
                'response_length': len(result['response'])
            })
            
            print(f"✓ Time: {processing_time:.2f}s, Cache: {result['cache_used']}")
            
        except Exception as e:
            print(f"✗ Failed: {e}")
            results.append({
                'query': query,
                'time': 0,
                'cache_used': False,
                'error': str(e)
            })
    
    # Summary
    print(f"\n--- Performance Summary ---")
    print(f"Total time: {total_time:.2f} seconds")
    print(f"Average time per query: {total_time/len(queries):.2f} seconds")
    
    cache_used_count = sum(1 for r in results if r.get('cache_used', False))
    print(f"Queries using cache: {cache_used_count}/{len(queries)}")
    
    if results:
        first_query_time = results[0]['time']
        subsequent_avg = sum(r['time'] for r in results[1:]) / max(1, len(results) - 1)
        print(f"First query time: {first_query_time:.2f}s")
        print(f"Subsequent queries avg: {subsequent_avg:.2f}s")
        
        if first_query_time > 0 and subsequent_avg > 0:
            speedup = first_query_time / subsequent_avg
            print(f"Speedup factor: {speedup:.1f}x")

def test_cache_management():
    """Test cache management functions"""
    print("\n" + "="*60)
    print("Cache Management Tests")
    print("="*60)
    
    test_file = "狂人日记.txt"
    
    if not os.path.exists(test_file):
        print(f"✗ Test file {test_file} not found")
        return
    
    # Ensure we have a cache file
    query = "测试缓存管理功能"
    ra.process_text_file(test_file, query, verbose=False)
    
    # Test cache info
    print("--- Cache Information ---")
    info = ra.get_cache_info(test_file)
    for key, value in info.items():
        print(f"  {key}: {value}")
    
    # Test cache clearing
    print("\n--- Cache Clearing ---")
    pkl_path = ra.get_cache_path(test_file)
    exists_before = os.path.exists(pkl_path)
    print(f"Cache exists before clearing: {exists_before}")
    
    ra.clear_cache(test_file, verbose=True)
    
    exists_after = os.path.exists(pkl_path)
    print(f"Cache exists after clearing: {exists_after}")

def main():
    """Main test function"""
    print("Read Agent Universal - Caching System Tests")
    print("="*60)
    
    # Run all tests
    test_cache_path_generation()
    test_cache_info()
    test_cache_validation()
    test_basic_caching()
    test_force_reprocess()
    test_multiple_queries_performance()
    test_cache_management()
    
    print("\n" + "="*60)
    print("All caching tests completed!")
    print("\nKey features verified:")
    print("✓ Automatic cache file generation (txt -> pkl)")
    print("✓ Cache validation (hash checking, version compatibility)")
    print("✓ Intelligent cache usage (load when valid, reprocess when invalid)")
    print("✓ Force reprocess option")
    print("✓ Significant performance improvement for multiple queries")
    print("✓ Cache management utilities")

if __name__ == "__main__":
    main()
