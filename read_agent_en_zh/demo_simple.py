#!/usr/bin/env python3
"""
Simple Demo for Read Agent Universal

This script demonstrates the basic usage of Read Agent Universal
with both Chinese and English text processing capabilities.
"""

import sys
import os

# Add current directory to path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

try:
    import read_agent_universal as ra
    print("✓ Read Agent Universal imported successfully")
except ImportError as e:
    print(f"✗ Failed to import Read Agent Universal: {e}")
    sys.exit(1)

def demo_basic_functions():
    """Demonstrate basic functions without API calls"""
    print("\n" + "="*60)
    print("Basic Functions Demo")
    print("="*60)
    
    # Test texts
    chinese_text = """
    人工智能技术发展迅速。深度学习算法不断进步，在图像识别、自然语言处理等领域取得重大突破。
    机器学习模型的性能持续提升。研究人员开发了更加高效的神经网络架构。
    未来，AI技术将在更多领域发挥重要作用。自动驾驶、医疗诊断、智能制造等应用前景广阔。
    """
    
    english_text = """
    Artificial intelligence technology is developing rapidly. Deep learning algorithms continue to advance,
    achieving major breakthroughs in image recognition and natural language processing.
    Machine learning model performance keeps improving. Researchers have developed more efficient neural network architectures.
    In the future, AI technology will play important roles in more fields. Applications like autonomous driving,
    medical diagnosis, and smart manufacturing have broad prospects.
    """
    
    mixed_text = """
    人工智能(AI)技术发展迅速。Machine learning和deep learning是核心技术！
    According to recent research, AI applications are expanding rapidly.
    根据最新研究，人工智能应用正在快速扩展。The future of technology looks promising.
    """
    
    test_cases = [
        ("Chinese Text", chinese_text),
        ("English Text", english_text),
        ("Mixed Text", mixed_text)
    ]
    
    for name, text in test_cases:
        print(f"\n--- {name} ---")
        
        # Language detection
        language = ra.detect_language(text)
        print(f"Language detected: {language}")
        
        # Text statistics
        stats = ra.get_text_statistics(text)
        print(f"Text units: {stats['total_units']}")
        print(f"Characters: {stats['total_characters']}")
        print(f"Chinese chars: {stats['chinese_chars']}")
        print(f"English words: {stats['english_words']}")
        
        # Text segmentation
        segments = ra.parse_text_universal(text, language)
        print(f"Segments: {len(segments)}")
        
        # Show first segment
        if segments:
            preview = segments[0][:50] + "..." if len(segments[0]) > 50 else segments[0]
            print(f"First segment: {preview}")

def demo_query_analysis():
    """Demonstrate query language detection"""
    print("\n" + "="*60)
    print("Query Analysis Demo")
    print("="*60)
    
    queries = [
        "What is the main topic of this document?",
        "这个文档的主要内容是什么？",
        "How does AI technology impact society?",
        "人工智能技术如何影响社会？",
        "AI技术的future发展趋势是什么？",  # Mixed query
    ]
    
    for query in queries:
        language = ra.detect_language(query)
        print(f"Query: {query}")
        print(f"Language: {language}")
        print()

def demo_prompt_generation():
    """Demonstrate prompt template generation"""
    print("\n" + "="*60)
    print("Prompt Generation Demo")
    print("="*60)
    
    test_cases = [
        ("gisting", "zh", {"page_text": "这是一段测试文本，用于演示摘要功能。"}),
        ("gisting", "en", {"page_text": "This is a test text for demonstrating summarization."}),
        ("lookup", "zh", {
            "gist_context": "文档摘要：讨论AI技术发展",
            "query": "AI技术有什么应用？"
        }),
        ("lookup", "en", {
            "gist_context": "Document summary: Discusses AI technology development",
            "query": "What are the applications of AI?"
        })
    ]
    
    for template_name, language, kwargs in test_cases:
        print(f"\n--- {template_name.title()} Template ({language.upper()}) ---")
        try:
            prompt = ra.get_prompt_with_language(template_name, language, **kwargs)
            # Show first 200 characters
            preview = prompt[:200] + "..." if len(prompt) > 200 else prompt
            print(preview)
        except Exception as e:
            print(f"Error: {e}")

def demo_file_processing():
    """Demonstrate file processing with 狂人日记"""
    print("\n" + "="*60)
    print("File Processing Demo")
    print("="*60)
    
    file_path = "狂人日记.txt"
    
    if not os.path.exists(file_path):
        print(f"✗ Test file not found: {file_path}")
        return
    
    try:
        # Load file
        with open(file_path, 'r', encoding='utf-8') as f:
            text = f.read()
        
        # Extract main content (skip Project Gutenberg header)
        start_marker = "一"
        start_idx = text.find(start_marker)
        if start_idx != -1:
            content = text[start_idx:start_idx+2000]  # First 2000 chars for demo
        else:
            content = text[:2000]
        
        print(f"✓ Loaded {file_path}")
        print(f"Content preview: {content[:100]}...")
        
        # Analyze content
        language = ra.detect_language(content)
        stats = ra.get_text_statistics(content)
        
        print(f"\nContent Analysis:")
        print(f"Language: {language}")
        print(f"Text units: {stats['total_units']}")
        print(f"Characters: {stats['total_characters']}")
        print(f"Chinese chars: {stats['chinese_chars']}")
        print(f"English words: {stats['english_words']}")
        
        # Text segmentation
        segments = ra.parse_text_universal(content, language)
        print(f"Segments: {len(segments)}")
        
        # Show first few segments
        print(f"\nFirst 3 segments:")
        for i, seg in enumerate(segments[:3], 1):
            preview = seg[:50] + "..." if len(seg) > 50 else seg
            print(f"  {i}: {preview}")
        
        # Simulate processing
        config = ra.DEFAULT_CONFIG[language]
        print(f"\nProcessing configuration for {language}:")
        print(f"  Unit limit: {config['unit_limit']}")
        print(f"  Start threshold: {config['start_threshold']}")
        print(f"  Max lookup pages: {config['max_lookup_pages']}")
        
        # Estimate pages
        total_units = stats['total_units']
        estimated_pages = (total_units // config['unit_limit']) + 1
        print(f"  Estimated pages: {estimated_pages}")
        
    except Exception as e:
        print(f"✗ Error processing file: {e}")

def demo_api_simulation():
    """Simulate API processing without actual API calls"""
    print("\n" + "="*60)
    print("API Processing Simulation")
    print("="*60)
    
    print("This demonstrates what would happen with API access:")
    print()
    
    sample_text = """
    人工智能技术发展迅速，深度学习算法不断进步。在图像识别、自然语言处理等领域取得重大突破。
    机器学习模型的性能持续提升，研究人员开发了更加高效的神经网络架构。
    未来，AI技术将在更多领域发挥重要作用。自动驾驶、医疗诊断、智能制造等应用前景广阔。
    """
    
    query = "AI技术有哪些应用领域？"
    
    print(f"Sample text: {sample_text[:100]}...")
    print(f"Query: {query}")
    print()
    
    # Simulate processing steps
    language = ra.detect_language(sample_text)
    print(f"1. ✓ Language detection: {language}")
    
    segments = ra.parse_text_universal(sample_text, language)
    print(f"2. ✓ Text segmentation: {len(segments)} segments")
    
    print("3. ✓ Text pagination: Would create pages based on unit limits")
    print("4. ✓ Gist generation: Would generate summaries for each page")
    print("5. ✓ Page lookup: Would find relevant pages based on query")
    print("6. ✓ Response generation: Would generate comprehensive answer")
    
    # Show what the result structure would look like
    print(f"\nResult structure would include:")
    print(f"  - pages: List of text pages")
    print(f"  - gist_memories: List of page summaries")
    print(f"  - relevant_pages: List of relevant page indices")
    print(f"  - response: Generated answer")
    print(f"  - compression_rate: Text compression ratio")
    print(f"  - language: Detected language ({language})")
    print(f"  - statistics: Text analysis results")

def main():
    """Main demo function"""
    print("Read Agent Universal - Simple Demo")
    print("="*60)
    print("This demo showcases the text analysis capabilities")
    print("without requiring API access.")
    print()
    
    # Run all demos
    demo_basic_functions()
    demo_query_analysis()
    demo_prompt_generation()
    demo_file_processing()
    demo_api_simulation()
    
    print("\n" + "="*60)
    print("Demo completed!")
    print()
    print("To use the full Read Agent functionality:")
    print("1. Ensure your local LLM API is running")
    print("2. Update API_CONFIG in read_agent_universal.py")
    print("3. Use: result = ra.process_long_text(text, query)")
    print()
    print("For more examples, see test_kuangren_diary.py")

if __name__ == "__main__":
    main()
