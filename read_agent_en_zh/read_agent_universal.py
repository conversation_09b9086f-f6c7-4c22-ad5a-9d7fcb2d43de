"""
Read Agent Universal - Chinese & English Long Text Processing

A unified implementation of the Read Agent algorithm for processing long texts
in Chinese and English, with seamless support for mixed-language content.

Based on the paper: "A Human-Inspired Reading Agent with Gist Memory of Very Long Contexts"
by <PERSON> et al. (2024)

Key Features:
- Punctuation-based text segmentation
- Unified prompt templates with language variables
- Automatic language detection
- Mixed Chinese-English content support
- No external NLP dependencies

Usage:
    import read_agent_universal as ra
    
    text = "Your long document content..."
    query = "What are the main points discussed?"
    
    result = ra.process_long_text(text, query)
    print(result['response'])
"""

import json
import pickle
import re
import time
import datetime
import os
from typing import List, Dict, Optional, Tuple, Union
import requests

# ==================== 1. 配置和常量 ====================

DEFAULT_CONFIG = {
    'zh': {
        'unit_limit': 1000,        # 1000 character units
        'start_threshold': 500,    # 500 character units
        'max_lookup_pages': 5,
        'temperature': 0.0,
        'unit_type': 'char_mixed'
    },
    'en': {
        'unit_limit': 600,         # 600 words
        'start_threshold': 280,    # 280 words
        'max_lookup_pages': 5,
        'temperature': 0.0,
        'unit_type': 'word'
    }
}

API_CONFIG = {
    'api_url': 'http://192.168.3.70:1234/v1/chat/completions',
    'api_key': 'lm_studio',
    'model': 'qwen3-30b-a3b',
    'max_tokens': 4096,
    'max_retries': 5
}

# Universal prompt templates with language variables
UNIVERSAL_PROMPT_TEMPLATES = {
    'pagination': """You are given a passage with numbered labels between paragraphs.
Please choose one label that marks a natural break point.
Such points can be scene transitions, end of dialogues, topic changes, etc.

Please respond in {language}.

Previous context: {previous_context}

Current passage:
{current_passage}

Next preview: {next_preview}

Please answer with "Break point: <number>" and explain your choice.""",

    'gisting': """Please shorten the following passage while preserving key information.
Just provide the shortened version without explanation.

Please respond in {language}.

Passage:
{page_text}""",

    'lookup': """Based on the following document summary and user query, determine which pages would be most relevant to review.

Please respond in {language}.

Document Summary:
{gist_context}

User Query: {query}

Please respond with page numbers in format: [1, 3, 5] and explain your reasoning briefly.""",

    'response': """Based on the following context from a long document, please provide a comprehensive answer to the user's query.

Please respond in {language}.

Context:
{context}

User Query: {query}

Instructions:
- Provide a detailed, informative response
- Structure your answer clearly with headings or bullet points when appropriate
- Reference the document content explicitly
- Ensure accuracy and avoid speculation beyond the context
- If context is insufficient, state this clearly

Answer:"""
}

# Language mapping for prompt templates
LANGUAGE_MAPPING = {
    'zh': 'Chinese (中文)',
    'en': 'English'
}

# Punctuation marks for text segmentation
SENTENCE_ENDINGS = {
    'primary': ['。', '！', '？', '……'],           # Strong breaks
    'secondary': ['；', '：'],                     # Medium breaks
    'english': ['.', '!', '?'],                   # English breaks
    'mixed': ['。', '！', '？', '；', '：', '……', '.', '!', '?', ';', ':']
}

# ==================== 2. 工具函数 ====================


def detect_language(text: str) -> str:
    """
    Automatic language detection based on character analysis

    Args:
        text: Input text to analyze

    Returns:
        'zh' for Chinese, 'en' for English
    """
    if not text.strip():
        return 'en'

    # Count Chinese characters
    chinese_chars = len(re.findall(r'[\u4e00-\u9fff]', text))
    total_chars = len(re.sub(r'\s', '', text))

    if total_chars == 0:
        return 'en'

    chinese_ratio = chinese_chars / total_chars

    # If more than 10% Chinese characters, treat as Chinese
    return 'zh' if chinese_ratio > 0.1 else 'en'


def count_text_units(text: str, language: str) -> int:
    """
    Count text units based on language

    Args:
        text: Input text
        language: 'zh' for Chinese, 'en' for English

    Returns:
        Number of text units
    """
    if language == 'zh':
        # Mixed counting: Chinese chars + English words + punctuation
        units = 0
        i = 0

        while i < len(text):
            char = text[i]

            if '\u4e00' <= char <= '\u9fff':
                # Chinese character = 1 unit
                units += 1
                i += 1
            elif char.isalpha():
                # English word = 1 unit (skip entire word)
                start_i = i
                while i < len(text) and (text[i].isalnum() or text[i] in '-_'):
                    i += 1
                if i > start_i:  # Only count if we actually found a word
                    units += 1
            elif char.isdigit():
                # Numbers = 0.5 unit
                while i < len(text) and (text[i].isdigit() or text[i] in '.,'):
                    i += 1
                units += 0.5
            else:
                # Punctuation and spaces = 0.1 unit
                units += 0.1
                i += 1

        return int(units)
    else:
        # English: word-based counting
        return len(text.split())


def get_text_statistics(text: str) -> Dict:
    """
    Get comprehensive text statistics

    Args:
        text: Input text

    Returns:
        Dictionary with text statistics
    """
    language = detect_language(text)

    return {
        'language': language,
        'total_units': count_text_units(text, language),
        'total_characters': len(text),
        'total_lines': len(text.split('\n')),
        'paragraphs': len([p for p in text.split('\n') if p.strip()]),
        'chinese_chars': len(re.findall(r'[\u4e00-\u9fff]', text)),
        'english_words': len(re.findall(r'[a-zA-Z]+', text))
    }


def is_sentence_ending(text: str, pos: int, language: str) -> bool:
    """
    Determine if a punctuation mark represents sentence ending

    Args:
        text: Full text
        pos: Position of punctuation mark
        language: Text language

    Returns:
        True if it's a sentence ending
    """
    if pos + 1 >= len(text):
        return True

    char = text[pos]
    next_char = text[pos + 1]

    # For English periods, check for abbreviations
    if char == '.' and language == 'en':
        # Check for common abbreviations
        if pos >= 2:
            prev_chars = text[max(0, pos-3):pos]
            common_abbrevs = ['Mr', 'Mrs', 'Dr',
                              'Prof', 'etc', 'vs', 'Inc', 'Ltd']
            for abbrev in common_abbrevs:
                if prev_chars.endswith(abbrev):
                    return False

        # If followed by space and capital letter, likely sentence end
        if next_char.isspace() and pos + 2 < len(text) and text[pos + 2].isupper():
            return True

        # If followed by Chinese character, likely sentence end
        if '\u4e00' <= next_char <= '\u9fff':
            return True

    # Chinese punctuation is generally reliable for sentence endings
    if char in ['。', '！', '？', '……']:
        return True

    # Secondary punctuation marks
    if char in ['；', '：'] and next_char.isspace():
        return True

    return False


# ==================== 3. 本地模型接口 ====================

def query_local_model(
    prompt: str,
    api_url: str = None,
    api_key: str = None,
    model: str = None,
    temperature: float = 0.0,
    max_tokens: int = 4096,
    max_retries: int = 5
) -> str:
    """
    Query local LLM API

    Args:
        prompt: Input prompt
        api_url: API endpoint URL
        api_key: API key
        model: Model name
        temperature: Temperature parameter
        max_tokens: Maximum tokens
        max_retries: Maximum retry attempts

    Returns:
        Model response text
    """
    # Use default config if not provided
    api_url = api_url or API_CONFIG['api_url']
    api_key = api_key or API_CONFIG['api_key']
    model = model or API_CONFIG['model']
    max_retries = max_retries or API_CONFIG['max_retries']

    headers = {
        'Authorization': f'Bearer {api_key}',
        'Content-Type': 'application/json'
    }

    payload = {
        'model': model,
        'messages': [{'role': 'user', 'content': prompt}],
        'temperature': temperature,
        'max_tokens': max_tokens
    }

    for attempt in range(max_retries):
        try:
            response = requests.post(
                api_url, headers=headers, json=payload, timeout=60)
            response.raise_for_status()
            data = response.json()
            return data['choices'][0]['message']['content']

        except requests.exceptions.Timeout:
            print(
                f'{datetime.datetime.now()}: Request timeout (attempt {attempt + 1}/{max_retries})')
            if attempt < max_retries - 1:
                time.sleep(5)
        except requests.exceptions.HTTPError as e:
            if e.response.status_code == 429:
                print(f'{datetime.datetime.now()}: Rate limit exceeded, waiting...')
                time.sleep(30)
            else:
                print(f'{datetime.datetime.now()}: HTTP error: {e}')
                if attempt < max_retries - 1:
                    time.sleep(5)
        except Exception as e:
            print(f'{datetime.datetime.now()}: Unexpected error: {e}')
            if attempt < max_retries - 1:
                time.sleep(5)

    raise Exception(f'Failed after {max_retries} attempts to query the API')


def get_prompt_with_language(template_name: str, language: str, **kwargs) -> str:
    """
    Get prompt template with language instruction

    Args:
        template_name: Name of the template
        language: Target language code
        **kwargs: Template variables

    Returns:
        Formatted prompt with language instruction
    """
    template = UNIVERSAL_PROMPT_TEMPLATES[template_name]
    language_instruction = LANGUAGE_MAPPING.get(language, 'English')

    return template.format(language=language_instruction, **kwargs)


# ==================== 4. 文本分割函数 ====================

def split_by_punctuation(text: str, language: str) -> List[str]:
    """
    Split text by punctuation marks

    Args:
        text: Input text
        language: Text language

    Returns:
        List of text segments
    """
    if not text.strip():
        return []

    segments = []
    current_segment = ""

    i = 0
    while i < len(text):
        char = text[i]
        current_segment += char

        # Check for sentence endings
        if char in SENTENCE_ENDINGS['mixed']:
            if is_sentence_ending(text, i, language):
                segments.append(current_segment.strip())
                current_segment = ""

        i += 1

    # Add remaining content
    if current_segment.strip():
        segments.append(current_segment.strip())

    return [seg for seg in segments if seg.strip()]


def split_text_by_paragraphs(text: str) -> List[str]:
    """
    Split text by paragraphs, handling Chinese indentation

    Args:
        text: Input text

    Returns:
        List of paragraphs
    """
    # Normalize line endings
    text = re.sub(r'\r\n', '\n', text)

    # Split by double newlines
    paragraphs = re.split(r'\n\s*\n', text)

    # Further split by Chinese indentation patterns
    result = []
    for para in paragraphs:
        para = para.strip()
        if para:
            # Split by lines that start with indentation
            sub_paras = re.split(r'\n(?=\s*[　\t])', para)
            result.extend([p.strip() for p in sub_paras if p.strip()])

    return result


def parse_text_universal(text: str, language: str) -> List[str]:
    """
    Universal text parsing for both Chinese and English

    Args:
        text: Raw input text
        language: Text language

    Returns:
        List of text segments ready for processing
    """
    # First split by paragraphs
    paragraphs = split_text_by_paragraphs(text)

    # Then split each paragraph by punctuation
    segments = []
    for para in paragraphs:
        para_segments = split_by_punctuation(para, language)
        segments.extend(para_segments)

    return segments


# ==================== 5. 解析函数 ====================

def clean_llm_response(response: str) -> str:
    """
    Clean LLM response by removing thinking tags and other artifacts

    Args:
        response: Raw LLM response

    Returns:
        Cleaned response text
    """
    if not response:
        return ""

    # Remove <think>...</think> blocks (case insensitive)
    response = re.sub(r'<think>.*?</think>', '', response,
                      flags=re.DOTALL | re.IGNORECASE)

    # Remove <thinking>...</thinking> blocks
    response = re.sub(r'<thinking>.*?</thinking>', '',
                      response, flags=re.DOTALL | re.IGNORECASE)

    # Remove other common thinking patterns
    response = re.sub(r'<thought>.*?</thought>', '',
                      response, flags=re.DOTALL | re.IGNORECASE)
    response = re.sub(r'<reasoning>.*?</reasoning>', '',
                      response, flags=re.DOTALL | re.IGNORECASE)

    # Remove markdown code blocks that might contain thinking
    response = re.sub(r'```thinking.*?```', '', response,
                      flags=re.DOTALL | re.IGNORECASE)
    response = re.sub(r'```thought.*?```', '', response,
                      flags=re.DOTALL | re.IGNORECASE)

    # Remove common prefixes that indicate thinking
    thinking_prefixes = [
        r'^(好的，我现在需要.*?。)',
        r'^(Let me think about this.*?\.)',
        r'^(I need to.*?\.)',
        r'^(First, I.*?\.)',
        r'^(Okay, I.*?\.)',
        r'^(好，我.*?。)',
        r'^(首先，我.*?。)',
    ]

    for prefix in thinking_prefixes:
        response = re.sub(prefix, '', response,
                          flags=re.MULTILINE | re.IGNORECASE)

    # Clean up extra whitespace
    # Multiple newlines to double
    response = re.sub(r'\n\s*\n\s*\n', '\n\n', response)
    response = response.strip()

    return response


def parse_pause_point(response: str) -> Optional[int]:
    """
    Parse pause point from model response

    Args:
        response: Model response text

    Returns:
        Pause point number or None
    """
    # Clean response first
    response = clean_llm_response(response)

    try:
        # Look for "Break point: <number>" pattern
        match = re.search(r'Break point:\s*<(\d+)>', response, re.IGNORECASE)
        if match:
            return int(match.group(1))

        # Look for "断点: <number>" pattern (Chinese)
        match = re.search(r'断点[:：]\s*<(\d+)>', response)
        if match:
            return int(match.group(1))

        # Look for "<number>" pattern
        match = re.search(r'<(\d+)>', response)
        if match:
            return int(match.group(1))

        # Look for just numbers after common phrases
        patterns = [
            r'(?:break point|断点)[:：]?\s*(\d+)',
            r'(?:选择|choose|select)[:：]?\s*(\d+)',
            r'(?:标签|label|tag)[:：]?\s*(\d+)'
        ]

        for pattern in patterns:
            match = re.search(pattern, response, re.IGNORECASE)
            if match:
                return int(match.group(1))

        return None
    except (ValueError, AttributeError):
        return None


def parse_page_ids(response: str) -> List[int]:
    """
    Parse page IDs from model response

    Args:
        response: Model response text

    Returns:
        List of page IDs
    """
    # Clean response first
    response = clean_llm_response(response)

    page_ids = []
    try:
        # Look for [number, number, ...] pattern
        match = re.search(r'\[([^\]]+)\]', response)
        if match:
            ids_str = match.group(1).split(',')
            for id_str in ids_str:
                id_str = re.sub(r'[^\d]', '', id_str)  # Keep only digits
                if id_str.isdigit():
                    page_ids.append(int(id_str))

        # Look for Chinese format: 第X页, 第Y页
        if not page_ids:
            chinese_pages = re.findall(r'第(\d+)页', response)
            page_ids = [int(num) for num in chinese_pages]

        # Look for "Page X, Page Y" format
        if not page_ids:
            english_pages = re.findall(r'[Pp]age\s+(\d+)', response)
            page_ids = [int(num) for num in english_pages]

        # If no specific format found, look for individual numbers
        if not page_ids:
            numbers = re.findall(r'\b(\d+)\b', response)
            # Filter out obviously wrong numbers (like years, large numbers)
            valid_numbers = [int(num)
                             for num in numbers if 0 <= int(num) <= 50]
            page_ids = valid_numbers[:5]  # Limit to 5

    except (ValueError, AttributeError):
        pass

    return page_ids


# ==================== 6. 核心处理函数 ====================

def create_page_universal(
    segments: List[str],
    start_idx: int,
    config: Dict,
    language: str
) -> Tuple[List[str], int]:
    """
    Create a single page from text segments

    Args:
        segments: List of text segments
        start_idx: Starting segment index
        config: Configuration dictionary
        language: Text language

    Returns:
        Tuple of (page_segments, next_start_index)
    """
    unit_limit = config.get('unit_limit', 600)
    start_threshold = config.get('start_threshold', 280)

    page = [segments[start_idx]]
    unit_count = count_text_units(segments[start_idx], language)
    j = start_idx + 1

    # Add segments until reaching unit limit
    while unit_count < unit_limit and j < len(segments):
        segment_units = count_text_units(segments[j], language)
        unit_count += segment_units

        if unit_count >= start_threshold:
            page.append(f"<{j}>")  # Add break point marker

        page.append(segments[j])
        j += 1

    page.append(f"<{j}>")

    # If page is too short, return as-is
    min_units = 100 if language == 'zh' else 50
    if unit_count < min_units:
        return segments[start_idx:j], j

    # Use LLM to decide optimal break point (if API is available)
    try:
        preceding = "" if start_idx == 0 else "...\n" + \
            '\n'.join(segments[max(0, start_idx-2):start_idx])
        end_preview = "" if j >= len(segments) else segments[j] + "\n..."

        prompt = get_prompt_with_language(
            'pagination',
            language,
            previous_context=preceding,
            current_passage='\n'.join(page),
            next_preview=end_preview
        )

        response = query_local_model(
            prompt, temperature=config.get('temperature', 0.0))

        # Clean response and parse pause point
        cleaned_response = clean_llm_response(response)
        pause_point = parse_pause_point(cleaned_response)

        if pause_point and start_idx < pause_point <= j:
            return segments[start_idx:pause_point], pause_point
        else:
            return segments[start_idx:j], j

    except Exception as e:
        if config.get('verbose', False):
            print(f"Pagination decision failed, using default split: {e}")
        return segments[start_idx:j], j


def paginate_text_universal(text: str, config: Dict, language: str) -> List[List[str]]:
    """
    Universal text pagination for both Chinese and English

    Args:
        text: Input text
        config: Configuration dictionary
        language: Text language

    Returns:
        List of pages (each page is a list of segments)
    """
    # Parse text into segments
    segments = parse_text_universal(text, language)

    pages = []
    i = 0

    while i < len(segments):
        page, next_i = create_page_universal(segments, i, config, language)
        pages.append(page)
        i = next_i

    if config.get('verbose', False):
        print(f"[Pagination] Created {len(pages)} pages for {language} text")

    return pages


def generate_gist_memories_universal(
    pages: List[List[str]],
    config: Dict,
    language: str
) -> List[str]:
    """
    Generate gist memories for all pages

    Args:
        pages: List of pages
        config: Configuration dictionary
        language: Text language

    Returns:
        List of gist memories
    """
    gist_memories = []

    for i, page in enumerate(pages):
        page_text = '\n'.join(page)

        try:
            prompt = get_prompt_with_language(
                'gisting',
                language,
                page_text=page_text
            )

            response = query_local_model(
                prompt, temperature=config.get('temperature', 0.0))

            # Clean the response to remove thinking tags and artifacts
            gist_memory = clean_llm_response(response)

            # Additional validation: ensure we have actual content
            if not gist_memory or len(gist_memory.strip()) < 10:
                raise ValueError("Generated gist is too short or empty")

            gist_memories.append(gist_memory)

            if config.get('verbose', False):
                # Show cleaned version in verbose output
                preview = gist_memory[:100] + \
                    "..." if len(gist_memory) > 100 else gist_memory
                print(f"[Gist] Page {i}: {preview}")

        except Exception as e:
            if config.get('verbose', False):
                print(
                    f"Gist generation failed for page {i}, using truncated text: {e}")
            # Fallback: use truncated original text
            fallback_length = 200 if language == 'zh' else 100
            gist_memory = page_text[:fallback_length] + \
                "..." if len(page_text) > fallback_length else page_text
            gist_memories.append(gist_memory)

    return gist_memories


def build_gist_context(gist_memories: List[str]) -> str:
    """
    Build gist memory context for lookup

    Args:
        gist_memories: List of gist memories

    Returns:
        Formatted gist context string
    """
    gist_context = []
    for i, gist in enumerate(gist_memories):
        gist_context.append(f"<Page {i}>\n{gist}")
    return '\n\n'.join(gist_context)


def find_relevant_pages_universal(
    gist_memories: List[str],
    query: str,
    config: Dict,
    language: str
) -> List[int]:
    """
    Find relevant pages based on query and gist memories

    Args:
        gist_memories: List of gist memories
        query: User query
        config: Configuration dictionary
        language: Text language

    Returns:
        List of relevant page indices
    """
    # Build gist context
    gist_context = build_gist_context(gist_memories)

    try:
        # Generate lookup prompt
        prompt = get_prompt_with_language(
            'lookup',
            language,
            gist_context=gist_context,
            query=query
        )

        # Query model
        response = query_local_model(
            prompt, temperature=config.get('temperature', 0.0))

        # Clean response and parse page IDs
        cleaned_response = clean_llm_response(response)
        page_ids = parse_page_ids(cleaned_response)

        # Validate page IDs
        valid_page_ids = []
        for page_id in page_ids:
            if 0 <= page_id < len(gist_memories):
                valid_page_ids.append(page_id)

        # Limit number of pages
        max_pages = config.get('max_lookup_pages', 5)
        valid_page_ids = valid_page_ids[:max_pages]

        if config.get('verbose', False):
            print(f"[Lookup] Selected pages: {valid_page_ids}")

        return valid_page_ids

    except Exception as e:
        if config.get('verbose', False):
            print(f"Page lookup failed, using first few pages: {e}")
        # Fallback: return first few pages
        max_pages = min(config.get('max_lookup_pages', 5), len(gist_memories))
        return list(range(max_pages))


def build_expanded_context(
    gist_memories: List[str],
    pages: List[List[str]],
    relevant_page_ids: List[int]
) -> str:
    """
    Build expanded context for response generation

    Args:
        gist_memories: List of gist memories
        pages: List of pages
        relevant_page_ids: List of relevant page indices

    Returns:
        Expanded context string
    """
    context_parts = []

    # Add all gist memories as background
    gist_context = build_gist_context(gist_memories)
    context_parts.append("Document Summary:")
    context_parts.append(gist_context)
    context_parts.append("\nDetailed Content from Relevant Pages:")

    # Add detailed content from relevant pages
    for page_id in relevant_page_ids:
        if 0 <= page_id < len(pages):
            page_content = '\n'.join(pages[page_id])
            context_parts.append(f"\n--- Page {page_id} (Detailed) ---")
            context_parts.append(page_content)

    return '\n'.join(context_parts)


def generate_response_universal(
    context: str,
    query: str,
    config: Dict,
    language: str
) -> str:
    """
    Generate response based on context and query

    Args:
        context: Expanded context
        query: User query
        config: Configuration dictionary
        language: Text language

    Returns:
        Generated response
    """
    try:
        prompt = get_prompt_with_language(
            'response',
            language,
            context=context,
            query=query
        )

        response = query_local_model(
            prompt, temperature=config.get('temperature', 0.0))

        # Clean the response to remove thinking tags and artifacts
        cleaned_response = clean_llm_response(response)
        return cleaned_response

    except Exception as e:
        if config.get('verbose', False):
            print(f"Response generation failed: {e}")
        return f"Sorry, I encountered an error while processing your query: {str(e)}"


# ==================== 7. 主要接口函数 ====================

def process_long_text(
    text: str,
    query: str,
    language: str = 'auto',
    verbose: bool = False,
    save_path: str = None,
    load_processed: str = None,
    **kwargs
) -> Dict:
    """
    Universal long text processing interface

    Args:
        text: Input text
        query: User query
        language: Language code ('auto', 'zh', 'en')
        verbose: Enable verbose output
        save_path: Path to save results
        load_processed: Path to load processed document
        **kwargs: Additional configuration parameters

    Returns:
        Dictionary containing processing results
    """

    # Auto-detect language if needed
    if language == 'auto':
        language = detect_language(text)

    # Get configuration for the language
    config = DEFAULT_CONFIG[language].copy()
    config.update(kwargs)
    config['verbose'] = verbose

    if verbose:
        print(
            f"[Process] Processing {language} text with {count_text_units(text, language)} units")

    # Try to load processed document
    pages = None
    gist_memories = None

    if load_processed and os.path.exists(load_processed):
        try:
            _, pages, gist_memories = load_processed_document(load_processed)
            if verbose:
                print(
                    f"[Load] Loaded processed document from {load_processed}")
        except Exception as e:
            if verbose:
                print(f"[Load] Failed to load processed document: {e}")

    # Process text if not loaded
    if pages is None or gist_memories is None:
        if verbose:
            print("[Process] Starting text processing...")

        # 1. Text pagination
        pages = paginate_text_universal(text, config, language)

        # 2. Generate gist memories
        gist_memories = generate_gist_memories_universal(
            pages, config, language)

        # Save processed document
        if save_path:
            try:
                save_processed_document(
                    text, pages, gist_memories, f"{save_path}_processed.pkl")
                if verbose:
                    print(
                        f"[Save] Saved processed document to {save_path}_processed.pkl")
            except Exception as e:
                if verbose:
                    print(f"[Save] Failed to save processed document: {e}")

    # 3. Find relevant pages
    relevant_pages = find_relevant_pages_universal(
        gist_memories, query, config, language)

    # 4. Build expanded context
    expanded_context = build_expanded_context(
        gist_memories, pages, relevant_pages)

    # 5. Generate response
    response = generate_response_universal(
        expanded_context, query, config, language)

    # Calculate compression rate
    original_units = count_text_units(text, language)
    gist_units = sum(count_text_units(gist, language)
                     for gist in gist_memories)
    compression_rate = 1.0 - \
        (gist_units / original_units) if original_units > 0 else 0.0

    # Build result
    result = {
        'pages': pages,
        'gist_memories': gist_memories,
        'relevant_pages': relevant_pages,
        'expanded_context': expanded_context,
        'response': response,
        'compression_rate': compression_rate,
        'language': language,
        'statistics': get_text_statistics(text)
    }

    # Save complete result
    if save_path:
        try:
            save_result(result, f"{save_path}_result.json")
            if verbose:
                print(f"[Save] Saved result to {save_path}_result.json")
        except Exception as e:
            if verbose:
                print(f"[Save] Failed to save result: {e}")

    return result


def process_text_file(
    file_path: str,
    query: str,
    language: str = 'auto',
    force_reprocess: bool = False,
    verbose: bool = False,
    **kwargs
) -> Dict:
    """
    处理文本文件的主要接口（支持自动缓存）

    Args:
        file_path: 文本文件路径
        query: 用户查询
        language: 语言设置 ('auto', 'zh', 'en')
        force_reprocess: 强制重新处理，忽略缓存
        verbose: 详细输出
        **kwargs: 其他配置参数

    Returns:
        处理结果字典
    """

    # 检查文件是否存在
    if not os.path.exists(file_path):
        raise FileNotFoundError(f"Text file not found: {file_path}")

    # 生成pkl文件路径
    pkl_path = get_cache_path(file_path)

    # 检查是否需要重新处理
    need_reprocess = force_reprocess or not validate_processed_file(
        file_path, pkl_path)

    if need_reprocess:
        if verbose:
            cache_info = get_cache_info(file_path)
            if cache_info['status'] == 'no_cache':
                print(f"[Process] No cache found, processing {file_path}...")
            elif cache_info['status'] == 'invalid':
                print(
                    f"[Process] Cache invalid (text modified), reprocessing {file_path}...")
            elif cache_info['status'] == 'corrupted':
                print(
                    f"[Process] Cache corrupted, reprocessing {file_path}...")
            elif force_reprocess:
                print(
                    f"[Process] Force reprocess enabled, processing {file_path}...")

        # 读取文本文件
        text = load_text_file(file_path)

        # 自动检测语言
        if language == 'auto':
            language = detect_language(text)

        # 获取配置
        config = DEFAULT_CONFIG[language].copy()
        config.update(kwargs)
        config['verbose'] = verbose

        if verbose:
            print(f"[Process] Detected language: {language}")
            print(f"[Process] Text length: {len(text)} characters")

        # 完整处理文档
        pages = paginate_text_universal(text, config, language)
        gist_memories = generate_gist_memories_universal(
            pages, config, language)

        # 保存处理结果到缓存
        save_processed_file_auto(
            file_path, text, pages, gist_memories, language, verbose)

    else:
        if verbose:
            print(f"[Cache] Loading cached result from {pkl_path}")

        # 加载缓存结果
        text, pages, gist_memories, language = load_processed_file_auto(
            pkl_path)

        # 获取配置
        config = DEFAULT_CONFIG[language].copy()
        config.update(kwargs)
        config['verbose'] = verbose

    # 处理查询（无论是否使用缓存，都需要重新生成响应）
    if verbose:
        print(f"[Query] Processing query: {query}")
        print(
            f"[Query] Using {len(pages)} pages, {len(gist_memories)} gist memories")

    # 查找相关页面
    relevant_pages = find_relevant_pages_universal(
        gist_memories, query, config, language)

    # 构建扩展上下文
    expanded_context = build_expanded_context(
        gist_memories, pages, relevant_pages)

    # 生成响应
    response = generate_response_universal(
        expanded_context, query, config, language)

    # 计算压缩率
    original_units = count_text_units(text, language)
    gist_units = sum(count_text_units(gist, language)
                     for gist in gist_memories)
    compression_rate = 1.0 - \
        (gist_units / original_units) if original_units > 0 else 0.0

    # 构建结果
    result = {
        'pages': pages,
        'gist_memories': gist_memories,
        'relevant_pages': relevant_pages,
        'expanded_context': expanded_context,
        'response': response,
        'compression_rate': compression_rate,
        'language': language,
        'statistics': get_text_statistics(text),
        'cache_used': not need_reprocess,
        'cache_path': pkl_path
    }

    if verbose:
        print(f"[Result] Compression rate: {compression_rate:.2%}")
        print(f"[Result] Cache used: {not need_reprocess}")
        print(f"[Result] Response length: {len(response)} characters")

    return result


# ==================== 8. 缓存管理函数 ====================

def get_cache_path(file_path: str) -> str:
    """
    生成缓存文件路径

    Args:
        file_path: 原始文本文件路径

    Returns:
        对应的pkl缓存文件路径
    """
    base_path = os.path.splitext(file_path)[0]
    return f"{base_path}.pkl"


def validate_processed_file(txt_path: str, pkl_path: str) -> bool:
    """
    验证pkl文件是否有效且与txt文件匹配

    Args:
        txt_path: 原始文本文件路径
        pkl_path: 缓存文件路径

    Returns:
        True if cache is valid, False otherwise
    """
    if not os.path.exists(pkl_path):
        return False

    try:
        # 检查pkl文件完整性
        with open(pkl_path, 'rb') as f:
            data = pickle.load(f)

        # 验证必要字段
        required_fields = ['original_text', 'pages',
                           'gist_memories', 'text_hash', 'version']
        if not all(field in data for field in required_fields):
            return False

        # 验证文本哈希值
        if not os.path.exists(txt_path):
            return False

        with open(txt_path, 'r', encoding='utf-8') as f:
            current_text = f.read()

        current_hash = hash(current_text)
        stored_hash = data.get('text_hash')

        if current_hash != stored_hash:
            return False

        # 验证处理完整性
        if not data['pages'] or not data['gist_memories']:
            return False

        # 验证版本兼容性
        if not check_cache_version(data):
            return False

        return True

    except Exception as e:
        return False


def check_cache_version(data: dict) -> bool:
    """
    检查缓存版本兼容性

    Args:
        data: 缓存数据字典

    Returns:
        True if version is compatible, False otherwise
    """
    cache_version = data.get('version', '1.0.0')
    current_version = "1.1.0"  # 当前系统版本

    try:
        # 简单的版本兼容性检查
        cache_major = int(cache_version.split('.')[0])
        current_major = int(current_version.split('.')[0])

        return cache_major == current_major

    except (ValueError, AttributeError):
        return False


def load_text_file(file_path: str) -> str:
    """
    加载文本文件

    Args:
        file_path: 文件路径

    Returns:
        文件内容
    """
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            return f.read()
    except UnicodeDecodeError:
        # 尝试其他编码
        try:
            with open(file_path, 'r', encoding='gbk') as f:
                return f.read()
        except UnicodeDecodeError:
            with open(file_path, 'r', encoding='latin-1') as f:
                return f.read()


def save_processed_file_auto(
    file_path: str,
    text: str,
    pages: List[List[str]],
    gist_memories: List[str],
    language: str,
    verbose: bool = False
) -> None:
    """
    自动保存处理结果到对应的pkl文件

    Args:
        file_path: 原始文本文件路径
        text: 原始文本
        pages: 处理后的页面
        gist_memories: 摘要记忆
        language: 检测到的语言
        verbose: 是否显示详细信息
    """
    pkl_path = get_cache_path(file_path)

    try:
        processed_doc = {
            'original_text': text,
            'pages': pages,
            'gist_memories': gist_memories,
            'text_hash': hash(text),
            'processed_time': datetime.datetime.now().isoformat(),
            'language': language,
            'statistics': get_text_statistics(text),
            'version': '1.1.0'  # 当前版本
        }

        with open(pkl_path, 'wb') as f:
            pickle.dump(processed_doc, f)

        if verbose:
            print(f"[Cache] Saved processed document to {pkl_path}")

    except Exception as e:
        if verbose:
            print(f"[Cache] Failed to save cache: {e}")


def load_processed_file_auto(pkl_path: str) -> Tuple[str, List[List[str]], List[str], str]:
    """
    自动加载处理结果

    Args:
        pkl_path: 缓存文件路径

    Returns:
        Tuple of (original_text, pages, gist_memories, language)
    """
    with open(pkl_path, 'rb') as f:
        doc = pickle.load(f)

    return (
        doc['original_text'],
        doc['pages'],
        doc['gist_memories'],
        doc['language']
    )


def get_cache_info(file_path: str) -> Dict:
    """
    获取缓存文件信息

    Args:
        file_path: 原始文本文件路径

    Returns:
        缓存信息字典
    """
    pkl_path = get_cache_path(file_path)

    if not os.path.exists(pkl_path):
        return {"status": "no_cache", "path": pkl_path}

    try:
        with open(pkl_path, 'rb') as f:
            data = pickle.load(f)

        is_valid = validate_processed_file(file_path, pkl_path)

        return {
            "status": "valid" if is_valid else "invalid",
            "path": pkl_path,
            "created_time": data.get('processed_time'),
            "version": data.get('version'),
            "pages": len(data.get('pages', [])),
            "language": data.get('language'),
            "file_size": os.path.getsize(pkl_path)
        }

    except Exception as e:
        return {"status": "corrupted", "path": pkl_path, "error": str(e)}


def clear_cache(file_path: str = None, verbose: bool = False) -> None:
    """
    清理缓存文件

    Args:
        file_path: 特定文件路径，如果为None则清理当前目录所有缓存
        verbose: 是否显示详细信息
    """
    if file_path:
        # 清理特定文件的缓存
        pkl_path = get_cache_path(file_path)
        if os.path.exists(pkl_path):
            os.remove(pkl_path)
            if verbose:
                print(f"[Cache] Removed {pkl_path}")
    else:
        # 清理当前目录下所有pkl缓存
        removed_count = 0
        for file in os.listdir('.'):
            if file.endswith('.pkl'):
                try:
                    os.remove(file)
                    removed_count += 1
                    if verbose:
                        print(f"[Cache] Removed {file}")
                except Exception as e:
                    if verbose:
                        print(f"[Cache] Failed to remove {file}: {e}")

        if verbose:
            print(f"[Cache] Removed {removed_count} cache files")


# ==================== 9. 持久化函数 ====================

def save_result(result: Dict, filepath: str, format: str = 'json') -> None:
    """
    Save processing result

    Args:
        result: Processing result dictionary
        filepath: Save path
        format: Save format ('json' or 'pickle')
    """
    result_copy = result.copy()
    result_copy['timestamp'] = datetime.datetime.now().isoformat()

    if format == 'json':
        # Prepare for JSON serialization
        json_result = result_copy.copy()
        # Truncate long context for JSON
        if 'expanded_context' in json_result and len(json_result['expanded_context']) > 10000:
            json_result['expanded_context'] = json_result['expanded_context'][:10000] + \
                "...[truncated]"

        with open(filepath, 'w', encoding='utf-8') as f:
            json.dump(json_result, f, indent=2, ensure_ascii=False)
    elif format == 'pickle':
        with open(filepath, 'wb') as f:
            pickle.dump(result_copy, f)
    else:
        raise ValueError("Format must be 'json' or 'pickle'")


def load_result(filepath: str, format: str = 'json') -> Dict:
    """
    Load saved result

    Args:
        filepath: File path
        format: File format ('json' or 'pickle')

    Returns:
        Loaded result dictionary
    """
    if format == 'json':
        with open(filepath, 'r', encoding='utf-8') as f:
            return json.load(f)
    elif format == 'pickle':
        with open(filepath, 'rb') as f:
            return pickle.load(f)
    else:
        raise ValueError("Format must be 'json' or 'pickle'")


def save_processed_document(
    text: str,
    pages: List[List[str]],
    gists: List[str],
    filepath: str
) -> None:
    """
    Save processed document for reuse

    Args:
        text: Original text
        pages: Processed pages
        gists: Gist memories
        filepath: Save path
    """
    processed_doc = {
        'original_text': text,
        'pages': pages,
        'gist_memories': gists,
        'text_hash': hash(text),
        'processed_time': datetime.datetime.now().isoformat(),
        'language': detect_language(text),
        'statistics': get_text_statistics(text)
    }

    with open(filepath, 'wb') as f:
        pickle.dump(processed_doc, f)


def load_processed_document(filepath: str) -> Tuple[str, List[List[str]], List[str]]:
    """
    Load processed document

    Args:
        filepath: File path

    Returns:
        Tuple of (original_text, pages, gist_memories)
    """
    with open(filepath, 'rb') as f:
        doc = pickle.load(f)
    return doc['original_text'], doc['pages'], doc['gist_memories']
