#!/usr/bin/env python3
"""
Read Agent Universal - Usage Examples

This file demonstrates the main usage patterns for Read Agent Universal
with the new caching functionality.
"""

import read_agent_universal as ra

def example_1_basic_usage():
    """Example 1: Basic file processing with automatic caching"""
    print("="*60)
    print("Example 1: Basic Usage with Automatic Caching")
    print("="*60)
    
    # Process a text file with automatic caching
    result = ra.process_text_file(
        file_path="狂人日记.txt",
        query="这个故事的主人公是谁？他有什么特点？",
        verbose=True
    )
    
    print(f"Response: {result['response'][:200]}...")
    print(f"Cache used: {result['cache_used']}")
    print(f"Compression rate: {result['compression_rate']:.2%}")

def example_2_multiple_queries():
    """Example 2: Multiple queries on the same document"""
    print("\n" + "="*60)
    print("Example 2: Multiple Queries (Cache Efficiency)")
    print("="*60)
    
    queries = [
        "故事的主题是什么？",
        "What social issues does this story reflect?",
        "作者想要表达什么观点？"
    ]
    
    for i, query in enumerate(queries, 1):
        print(f"\n--- Query {i}: {query} ---")
        
        result = ra.process_text_file(
            file_path="狂人日记.txt",
            query=query,
            verbose=False  # Reduce output for multiple queries
        )
        
        print(f"Cache used: {result['cache_used']}")
        print(f"Response preview: {result['response'][:100]}...")

def example_3_cache_management():
    """Example 3: Cache management operations"""
    print("\n" + "="*60)
    print("Example 3: Cache Management")
    print("="*60)
    
    file_path = "狂人日记.txt"
    
    # Check cache information
    cache_info = ra.get_cache_info(file_path)
    print("Cache Information:")
    for key, value in cache_info.items():
        print(f"  {key}: {value}")
    
    # Force reprocessing (ignore cache)
    print("\n--- Force Reprocessing ---")
    result = ra.process_text_file(
        file_path=file_path,
        query="测试强制重新处理",
        force_reprocess=True,
        verbose=True
    )
    print(f"Forced reprocess - Cache used: {result['cache_used']}")

def example_4_different_languages():
    """Example 4: Processing different language queries"""
    print("\n" + "="*60)
    print("Example 4: Mixed Language Queries")
    print("="*60)
    
    # Chinese query
    result_zh = ra.process_text_file(
        file_path="狂人日记.txt",
        query="这个故事反映了什么社会问题？",
        language='zh',  # Explicitly set language
        verbose=False
    )
    
    # English query
    result_en = ra.process_text_file(
        file_path="狂人日记.txt",
        query="How does the narrator's mental state change?",
        language='en',  # Explicitly set language
        verbose=False
    )
    
    print("Chinese Query:")
    print(f"  Query language: zh")
    print(f"  Response language: {ra.detect_language(result_zh['response'])}")
    print(f"  Cache used: {result_zh['cache_used']}")
    
    print("\nEnglish Query:")
    print(f"  Query language: en")
    print(f"  Response language: {ra.detect_language(result_en['response'])}")
    print(f"  Cache used: {result_en['cache_used']}")

def example_5_error_handling():
    """Example 5: Error handling and edge cases"""
    print("\n" + "="*60)
    print("Example 5: Error Handling")
    print("="*60)
    
    # Test with non-existent file
    try:
        result = ra.process_text_file(
            file_path="non_existent_file.txt",
            query="This will fail"
        )
    except FileNotFoundError as e:
        print(f"✓ Correctly handled missing file: {e}")
    
    # Test cache clearing
    print("\n--- Cache Clearing ---")
    ra.clear_cache("狂人日记.txt", verbose=True)
    
    # Verify cache is cleared
    cache_info = ra.get_cache_info("狂人日记.txt")
    print(f"Cache status after clearing: {cache_info['status']}")

def main():
    """Run all examples"""
    print("Read Agent Universal - Usage Examples")
    print("Demonstrating file processing with automatic caching")
    
    try:
        example_1_basic_usage()
        example_2_multiple_queries()
        example_3_cache_management()
        example_4_different_languages()
        example_5_error_handling()
        
        print("\n" + "="*60)
        print("All examples completed successfully!")
        print("\nKey takeaways:")
        print("✓ First query processes document and creates cache")
        print("✓ Subsequent queries use cache for 3x+ speedup")
        print("✓ Cache automatically validates text changes")
        print("✓ Supports both Chinese and English queries")
        print("✓ Robust error handling and cache management")
        
    except Exception as e:
        print(f"\n✗ Example failed: {e}")
        print("Make sure:")
        print("1. 狂人日记.txt exists in current directory")
        print("2. Local LLM API is running and accessible")
        print("3. API configuration is correct in read_agent_universal.py")

if __name__ == "__main__":
    main()
